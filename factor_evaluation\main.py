import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
# from iid import TimeSeriesHomogeneityTest
from statsmodels.tsa.stattools import adfuller
from data_service import DataService
from group_stats import GroupStats
from factor_dist import analyze_factor_distribution
import matplotlib.pyplot as plt


def traditional_cta_sig(df):
    return df['close']


if __name__ == '__main__':

    # ========数据处理=============================================================#
    '''
    ds['table','method']
    table:需要读取的币对数据(币对_频率_起始时间_结束事件)
    method:需要的因子名字段(默认是因子库中的数据,也可以用自定义函数把因子传到df['signals']中)
    '''

    ds = DataService()
    currency = 'ETHUSDT_15m_2020_2025'
    df = ds[currency]
    df = df['2021-10-1':]
    df['signals'] = traditional_cta_sig(df)
    df['r96'] = df['close'].shift(-1)/df['close']-1
    df = df.dropna()

    # ========平稳性检验=============================================================#

    # 进行 ADF 单位根检验
    adf_result = adfuller(df['signals'], autolag='AIC')
    p_value = adf_result[1]

    # 设置判断同质性的 p 值阈值 (例如 0.05)
    alpha = 0.05

    print("="*40)
    print(f'p_value : {p_value}')
    print("="*40)

    # ========pearson相关系数计算=============================================================#
    ic = df['signals'].corr(df['r96'], method='pearson')
    print(f'IC : {ic}')
    print("="*40)

    # ========spearman相关系数计算=============================================================#
    rank_ic = df['signals'].corr(df['r96'], method='spearman')
    print(f'Rank_IC : {rank_ic}')
    print("="*40)

    # ========IR计算（10组）=============================================================#
    n = 10
    group_labels = pd.qcut(df['signals'], n, labels=False, duplicates='drop')
    grouped = df.groupby(group_labels)[['signals', 'r96']]

    pearson_corrs_pd = []
    for _, group in grouped:
        corr_matrix = group.corr()
        pearson_corrs_pd.append(corr_matrix.iloc[0, 1])

    ir_value_pd = np.mean(pearson_corrs_pd) / np.std(pearson_corrs_pd)
    print(f'IR : {ir_value_pd}')
    print("="*40)

    # ========分组收益分析计算=============================================================#
    analyzer = GroupStats(df, value_col='signals', return_col='r96')
    stats, fig = analyzer.analyze_groups(n_groups=20, show_plot=True)
    # print(stats)
    # print("="*40)

    # ========因子分布情况=============================================================#
    result = analyze_factor_distribution(df)

    # n = 10  # 总窗口数
    # for window_id in range(n):
    #     print(f"\n窗口 {window_id+1}/{n}")
    #     print("="*40)
    #     print("时间范围:", result['window_stats'][n]['time_ranges'][window_id])

    #     # 获取当前窗口的统计指标
    #     stats = result['window_stats'][n]['stats'].iloc[window_id]

    #     # 格式化打印每个指标
    #     print("\n统计指标:")
    #     for metric, value in stats.items():
    #         print(f"{metric:<10}: {value:>10.4f}")  # 左对齐指标名，右对齐数值

    #     print("="*40)

    df['signals'].hist(bins=100)
    plt.show()
    df['turnover'].corr(df['volume'])
    df['signals'].describe()
    df['signals'].value_counts()

    ds = DataService()
    df_doge = ds['TRXUSDT_15m_2020_2025']['2024-11-11':'2024-11-14']
    df_temp = (df_doge['close']/df_doge['close'].shift(1)-1)
    df_doge['close'].plot()
