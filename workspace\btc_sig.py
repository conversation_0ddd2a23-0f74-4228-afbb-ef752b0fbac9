import pandas as pd
import numpy as np
from sqlalchemy import create_engine
import matplotlib.pyplot as plt

MYSQL_CONFIG = {
    "host": "************",
    "user": "root",
    "password": "1234",
    "database": "ctadata"
}


class DataManager():

    def __init__(self):
        # self.__tim__ = ticker.TickerInfoManager()

        self.problem_ticker = []
        self.engine = create_engine(f"mysql+pymysql://{MYSQL_CONFIG['user']}:{
                                    MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}/{MYSQL_CONFIG['database']}")
        return

    def concat_data(self, data):
        """读取并合并单品类永续合约的K线数据"""
        query = f"SELECT * FROM {data.contract}"
        df = pd.read_sql(query, self.engine)
        df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
        df.set_index('open_time', inplace=True)
        # 将所有K线数据合并
        data.data = df
        return


class Data():

    def __init__(self, contract):
        self.contract = contract
        return



df=pd.read_csv('btc_distribution2.csv')
# 转换为 datetime 类型，并对齐到分钟级别
df['时间'] = pd.to_datetime(df['时间']).dt.tz_convert(None).dt.ceil('T')
# 提取数值部分，去掉千位分隔符，并转换为浮点数
df['量']= df['量'].str.extract(r'([+-]?\d[\d,.]*)')[0].str.replace(',', '', regex=True).astype(float)
# a= df['量'].str.extract(r'([+-]?\d[\d,.]*)')[0].str.replace(',', '', regex=True).astype(float)
df = df.iloc[::-1].reset_index(drop=True)  # 方法1：直接反转索引
df2=df[['时间','量']]
df2['sig']=np.where(df['量'] > 0, 1, -1)
df2 = df2.rename(columns={'时间': 'open_time'})  # 统一时间列名

currency = 'BTCUSDT_1m_2021_2021'
d = Data(currency)
dm = DataManager()
dm.concat_data(d)
dfp=d.data.copy()
# 合并两个 DataFrame，以 df2 为主表
df_merged = dfp.merge(df2[['open_time', 'sig']], on='open_time', how='left')

# 对未匹配到的时间点填充 0
df_merged['sig'] = df_merged['sig'].fillna(0).astype(int)
df_merged['sig_decay'] = df_merged['sig'].ewm(span=100, adjust=False).mean()

df_merged['return']=df_merged['close'].shift(-1)/df_merged['close']-1



# 筛选 sig = 1 的行
sig_1_rows = df_merged[df_merged['sig'] == 1]

# 存储每个 sig = 1 后 100 个 'return' 的累积和
cumsum_list = []

# 遍历每个 sig = 1 的行，并计算后续100期的 cumsum
for idx in sig_1_rows.index:
    if idx + 100 < len(df_merged):  # 确保有足够的 100 行
        cumsum_values = df_merged.loc[idx:idx+99, 'return'].cumsum()
        cumsum_list.append(cumsum_values.values)

# 将所有 cumsum 曲线转换为 numpy 数组，进行行的平均
cumsum_array = np.array(cumsum_list)
average_cumsum = cumsum_array.mean(axis=0)

# 绘制折线图
plt.plot(range(1, 101), average_cumsum, label='Average Cumulative Return', color='blue')
plt.xlabel('Time (Periods After sig=1)')
plt.ylabel('Cumulative Return')
plt.title('Average Cumulative Return after sig = 1 over 100 Periods')
plt.legend()
plt.grid(True)
plt.show()



# 筛选 sig = -1 的行
sig_1_rows = df_merged[df_merged['sig'] == -1]

# 存储每个 sig = 1 后 100 个 'return' 的累积和
cumsum_list = []

# 遍历每个 sig = 1 的行，并计算后续100期的 cumsum
for idx in sig_1_rows.index:
    if idx + 100 < len(df_merged):  # 确保有足够的 100 行
        cumsum_values = -df_merged.loc[idx:idx+99, 'return'].cumsum()
        cumsum_list.append(cumsum_values.values)

# 将所有 cumsum 曲线转换为 numpy 数组，进行行的平均
cumsum_array = np.array(cumsum_list)
average_cumsum = cumsum_array.mean(axis=0)

# 绘制折线图
plt.plot(range(1, 101), average_cumsum, label='Average Cumulative Return', color='blue')
plt.xlabel('Time (Periods After sig=1)')
plt.ylabel('Cumulative Return')
plt.title('Average Cumulative Return after sig = 1 over 100 Periods')
plt.legend()
plt.grid(True)
plt.show()


















