import matplotlib.pyplot as plt
import pandas as pd
import seaborn as sns
import numpy as np


class GroupStats:
    def __init__(self, df, value_col, return_col):
        """
        初始化分析器
        :param df: 包含待分析数据的DataFrame
        :param value_col: 要分析的因子列名
        :param return_col: 目标收益率列名
        """
        self.df = df.copy()
        self.value_col = value_col
        self.return_col = return_col
        self._validate_columns()

    def _validate_columns(self):
        """验证必要列是否存在"""
        required_cols = [self.value_col, self.return_col]
        missing = [col for col in required_cols if col not in self.df.columns]
        if missing:
            raise ValueError(f"缺失必要列: {missing}")

    def analyze_groups(self, n_groups=10, show_plot=False):
        """
        综合分析方法（合并原分组和极端值分析）
        :param n_groups: 分组数量（2时等效极端值分析）
        :param show_plot: 是否显示分布图
        :return: (分组统计, 绘图对象)
        """
        # 动态分组
        self.df['group'] = pd.qcut(
            self.df[self.value_col],
            n_groups,
            labels=False,
            duplicates='drop'
        )

        # 分组统计（含极端组分析）
        stats = self.df.groupby('group').agg({
            self.value_col: ['min', 'max', 'mean'],
            self.return_col: ['mean', 'std', 'count']
        })

        # 重命名列
        stats.columns = ['val_min', 'val_max', 'val_mean',
                         'return_mean', 'return_std', 'count']

        # 绘制分布图
        fig = self._plot_distribution(n_groups, show_plot)

        return stats, fig

    def _plot_distribution(self, n_groups, show_plot):
        """绘制分组收益均值柱状图（保持接口不变）"""
        plt.figure(figsize=(12, 6))

        # 计算每组的收益均值
        group_returns = self.df.groupby('group')[self.return_col].mean()

        # 绘制柱状图
        bars = plt.bar(
            x=group_returns.index,
            height=group_returns.values,
            width=0.6,
            color='skyblue',
            edgecolor='navy',
            linewidth=1
        )

        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height,
                     f'{height:.4f}',
                     ha='center', va='bottom',
                     fontsize=9)

        # 设置图表样式
        plt.title(f'Mean {self.return_col} by Group ({n_groups} groups)')
        plt.xlabel('Group Number')
        plt.ylabel(f'Mean {self.return_col}')
        plt.xticks(range(n_groups))
        plt.grid(axis='y', linestyle='--', alpha=0.5)

        if show_plot:
            plt.show()
        return plt.gcf()
