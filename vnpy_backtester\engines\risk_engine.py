"""
Risk management engine for backtesting
"""

from typing import Dict, List, Optional, Callable
from datetime import datetime, time

from vnpy_backtester.utils.constant import Direction
from vnpy_backtester.objects.object import OrderData, TradeData, PositionData


class RiskEngine:
    """
    Risk management engine for backtesting.
    """

    def __init__(self) -> None:
        """
        Initialize risk engine.
        """
        self.active: bool = True

        # Position risk limits
        self.max_position: int = 100

        # Order risk limits
        self.max_order_size: int = 100
        self.max_order_value: float = 1000000

        # Trading time risk limits
        self.trading_start: time = time(9, 0)
        self.trading_end: time = time(15, 0)

        # Drawdown risk limits
        self.max_drawdown_percent: float = 0.3
        self.max_drawdown_money: float = 100000

        # Callback for risk events
        self.risk_callback: Callable = None

        # Current positions
        self.positions: Dict[str, PositionData] = {}

        # Current account balance
        self.balance: float = 0
        self.max_balance: float = 0

        # Trading statistics
        self.total_orders: int = 0
        self.total_trades: int = 0

    def set_position_limit(self, max_position: int) -> None:
        """
        Set maximum position limit.
        """
        self.max_position = max_position

    def set_order_limits(self, max_size: int, max_value: float) -> None:
        """
        Set order size and value limits.
        """
        self.max_order_size = max_size
        self.max_order_value = max_value

    def set_trading_time(self, start: time, end: time) -> None:
        """
        Set trading time limits.
        """
        self.trading_start = start
        self.trading_end = end

    def set_drawdown_limits(self, percent: float, money: float) -> None:
        """
        Set drawdown limits.
        """
        self.max_drawdown_percent = percent
        self.max_drawdown_money = money

    def set_risk_callback(self, callback: Callable) -> None:
        """
        Set callback for risk events.
        """
        self.risk_callback = callback

    def update_position(self, position: PositionData) -> None:
        """
        Update position data.
        """
        self.positions[position.vt_positionid] = position

    def update_balance(self, balance: float) -> None:
        """
        Update account balance.
        """
        self.balance = balance
        self.max_balance = max(self.max_balance, balance)

    def check_order_risk(self, order: OrderData) -> bool:
        """
        Check if order satisfies risk requirements.
        """
        # Check if risk engine is active
        if not self.active:
            return True

        # Check order size
        if order.volume > self.max_order_size:
            if self.risk_callback:
                self.risk_callback(
                    f"Order size {order.volume} exceeds limit {self.max_order_size}")
            return False

        # Check order value
        order_value = order.volume * order.price
        if order_value > self.max_order_value:
            if self.risk_callback:
                self.risk_callback(
                    f"Order value {order_value} exceeds limit {self.max_order_value}")
            return False

        # Check trading time
        if order.datetime and order.datetime.time() < self.trading_start:
            if self.risk_callback:
                self.risk_callback(
                    f"Order time {order.datetime.time()} before trading start {self.trading_start}")
            return False

        if order.datetime and order.datetime.time() > self.trading_end:
            if self.risk_callback:
                self.risk_callback(
                    f"Order time {order.datetime.time()} after trading end {self.trading_end}")
            return False

        # Check position limit
        position = self.positions.get(
            f"{order.gateway_name}.{order.vt_symbol}.{order.direction.value}", None)
        current_position = position.volume if position else 0

        if order.direction == Direction.LONG and current_position + order.volume > self.max_position:
            if self.risk_callback:
                self.risk_callback(
                    f"Long position {current_position + order.volume} exceeds limit {self.max_position}")
            return False

        if order.direction == Direction.SHORT and current_position + order.volume > self.max_position:
            if self.risk_callback:
                self.risk_callback(
                    f"Short position {current_position + order.volume} exceeds limit {self.max_position}")
            return False

        # Check drawdown
        if self.max_balance > 0:
            drawdown = self.max_balance - self.balance
            drawdown_percent = drawdown / self.max_balance

            if drawdown > self.max_drawdown_money:
                if self.risk_callback:
                    self.risk_callback(
                        f"Drawdown {drawdown} exceeds limit {self.max_drawdown_money}")
                return False

            if drawdown_percent > self.max_drawdown_percent:
                if self.risk_callback:
                    self.risk_callback(
                        f"Drawdown percent {drawdown_percent} exceeds limit {self.max_drawdown_percent}")
                return False

        return True

    def on_order(self, order: OrderData) -> None:
        """
        Callback when order is created.
        """
        self.total_orders += 1

    def on_trade(self, trade: TradeData) -> None:
        """
        Callback when trade is created.
        """
        self.total_trades += 1
