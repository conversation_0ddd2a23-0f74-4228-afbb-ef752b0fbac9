"""
Data objects used in the backtester
"""

from dataclasses import dataclass
from datetime import datetime as dt
from typing import Optional, List, Dict, Any

from vnpy_backtester.utils.constant import Direction, Offset, Exchange, Status, Interval


@dataclass
class BaseData:
    """
    Base data class for all data types.
    """
    gateway_name: str


@dataclass
class TickData(BaseData):
    """
    Tick data contains information about:
    * last trade in market
    * orderbook snapshot
    * intraday market statistics.
    """
    symbol: str
    exchange: Exchange
    datetime: dt

    name: Optional[str] = ""
    volume: float = 0
    turnover: float = 0
    open_interest: float = 0
    last_price: float = 0
    last_volume: float = 0
    limit_up: float = 0
    limit_down: float = 0

    open_price: float = 0
    high_price: float = 0
    low_price: float = 0
    pre_close: float = 0

    bid_price_1: float = 0
    bid_price_2: float = 0
    bid_price_3: float = 0
    bid_price_4: float = 0
    bid_price_5: float = 0

    ask_price_1: float = 0
    ask_price_2: float = 0
    ask_price_3: float = 0
    ask_price_4: float = 0
    ask_price_5: float = 0

    bid_volume_1: float = 0
    bid_volume_2: float = 0
    bid_volume_3: float = 0
    bid_volume_4: float = 0
    bid_volume_5: float = 0

    ask_volume_1: float = 0
    ask_volume_2: float = 0
    ask_volume_3: float = 0
    ask_volume_4: float = 0
    ask_volume_5: float = 0

    def __post_init__(self):
        """"""
        self.vt_symbol = f"{self.symbol}.{self.exchange.value}"


@dataclass
class BarData(BaseData):
    """
    Candlestick bar data of a certain trading period.
    """
    symbol: str
    exchange: Exchange
    datetime: dt

    open_price: float = 0
    high_price: float = 0
    low_price: float = 0
    close_price: float = 0
    volume: float = 0
    turnover: float = 0
    open_interest: float = 0

    def __post_init__(self):
        """"""
        self.vt_symbol = f"{self.symbol}.{self.exchange.value}"


@dataclass
class OrderData(BaseData):
    """
    Order data contains information for tracking lastest status
    of a specific order.
    """
    symbol: str
    exchange: Exchange
    orderid: str

    type: str = ""
    direction: Direction = Direction.LONG
    offset: Offset = Offset.OPEN
    price: float = 0
    volume: float = 0
    traded: float = 0
    status: Status = Status.SUBMITTING
    datetime: Optional[dt] = None
    reference: str = ""
    strategy_name: str = ""

    def __post_init__(self):
        """"""
        self.vt_symbol = f"{self.symbol}.{self.exchange.value}"
        self.vt_orderid = f"{self.gateway_name}.{self.orderid}"

    def is_active(self) -> bool:
        """
        Check if the order is active.
        """
        return self.status in [Status.SUBMITTING, Status.NOTTRADED, Status.PARTTRADED]


@dataclass
class TradeData(BaseData):
    """
    Trade data contains information of a fill of an order. One order
    can have multiple trade fills.
    """
    symbol: str
    exchange: Exchange
    orderid: str
    tradeid: str
    direction: Direction

    offset: Offset = Offset.OPEN
    price: float = 0
    volume: float = 0
    datetime: Optional[dt] = None

    def __post_init__(self):
        """"""
        self.vt_symbol = f"{self.symbol}.{self.exchange.value}"
        self.vt_orderid = f"{self.gateway_name}.{self.orderid}"
        self.vt_tradeid = f"{self.gateway_name}.{self.tradeid}"


@dataclass
class PositionData(BaseData):
    """
    Position data is used for tracking each individual position holding.
    """
    symbol: str
    exchange: Exchange
    direction: Direction

    volume: float = 0
    frozen: float = 0
    price: float = 0
    pnl: float = 0
    yd_volume: float = 0

    def __post_init__(self):
        """"""
        self.vt_symbol = f"{self.symbol}.{self.exchange.value}"
        self.vt_positionid = f"{self.gateway_name}.{self.vt_symbol}.{self.direction.value}"


@dataclass
class AccountData(BaseData):
    """
    Account data contains information about balance, frozen and
    available.
    """
    accountid: str

    balance: float = 0
    frozen: float = 0

    def __post_init__(self):
        """"""
        self.available = self.balance - self.frozen
        self.vt_accountid = f"{self.gateway_name}.{self.accountid}"
