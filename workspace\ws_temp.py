import websocket
import json
import os

# 设置环境变量（指向 SOCKS5 代理）
os.environ["ALL_PROXY"] = "socks5://127.0.0.1:7897"
os.environ["HTTPS_PROXY"] = "socks5://127.0.0.1:7897"

def on_message(ws, message):
    data = json.loads(message)
    print("data:", data)
    if data['k']['x']:
        print("close:", data['k'])
        #signal = model.predict(data)
        #log_print("signal:", signal)
        # if signal!=0:
        #    log_print("send Data:", signal)
        #    send_signal(signal,19)
            

def on_error(ws, error):
    print("Error:", error)
    pass

def on_close(ws, close_status_code, close_msg):
    print("Closed")

def on_open(ws):
    # 示例订阅 BTCUSDT 的1分钟K线数据
    payload = {
        "method": "SUBSCRIBE",
        "params": ["ethusdt_perpetual@continuousKline_15m"],
        # "params": ["ethusdt@depth20"],
        "id": 1
    }
    ws.send(json.dumps(payload))

url = "wss://fstream.binance.com/ws"
ws = websocket.WebSocketApp(url,
                            on_message=on_message,
                            on_error=on_error,
                            on_close=on_close)
ws.on_open = on_open


ws.run_forever()

