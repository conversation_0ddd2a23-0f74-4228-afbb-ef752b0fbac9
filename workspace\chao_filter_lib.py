import pandas as pd
import numpy as np


def F_historical_volatility(d):
    '''衡量当前波动率高低的过滤器'''
    df = d.copy()
    df['HV'] = np.log(
        (df['close']/df['close'].shift(1))).rolling(20).std()
    return df['HV']


def F_volume_deviation(d):
    '''衡量当前成交量高低的过滤器'''
    df = d.copy()
    df['volume_deviation'] = (
        df['volume']-df['volume'].rolling(20).mean())/df['volume'].rolling(20).mean()
    return df['volume_deviation']


def F_price_position(d):
    '''衡量当前相对位置高低的过滤器'''
    df = d.copy()
    df['up'] = df['high'].rolling(20).max()
    df['down'] = df['low'].rolling(20).min()
    df['price_position'] = (df['close']-df['down'])/(df['up']-df['down'])
    return df['price_position']


def F_price_fluctuation(d):
    '''衡量短期价格波动快慢的过滤器'''
    df = d.copy()
    df['price_fluctuation'] = df['close'].rolling(
        5).std()/df['close'].rolling(30).std()
    return df['price_fluctuation']
