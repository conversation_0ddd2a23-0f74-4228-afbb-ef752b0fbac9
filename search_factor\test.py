from gplearn_factor_extraction import FactorMining
from sqlalchemy import create_engine
from math_operators import MathOperators
import pandas as pd
import numpy as np
from time import time
MYSQL_CONFIG = {
    "host": "************",
    "user": "root",
    "password": "1234",
    "database": "ctadata"
}


class DataManager():

    def __init__(self):
        # self.__tim__ = ticker.TickerInfoManager()

        self.problem_ticker = []
        self.engine = create_engine(f"mysql+pymysql://{MYSQL_CONFIG['user']}:{
                                    MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}/{MYSQL_CONFIG['database']}")
        return

    def concat_data(self, data):
        """读取并合并单品类永续合约的K线数据"""
        query = f"SELECT * FROM {data.contract}"
        df = pd.read_sql(query, self.engine)
        df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
        df.set_index('open_time', inplace=True)
        # 将所有K线数据合并
        data.data = df
        return


class Data():

    def __init__(self, contract):
        self.contract = contract
        return


if __name__ == "__main__":
    currency = 'ETHUSDT_15m_2022_2025'
    d = Data(currency)
    dm = DataManager()
    dm.concat_data(d)
    df = d.data[['open', 'high', 'low', 'close',]].copy()
    # 初始化并运行因子挖掘
    features = ["open", "high", "low", "close",]
    # features = list(df.columns)
    target_column = "close"

    start_time = time()
    factor_miner = FactorMining(
        data=df,
        features=features,

        target_column=target_column,
        target_period=96,                          # 预测 target_period 天后的收益
        population_size=200,                        # 种群规模
        generations=10,                             # 进化代数
        function_set=('add', 'sub', 'mul', 'div',
                      'sqrt', 'log', 'abs'),        # 表达式
        test_size=0.2,
        random_state=42,
        metric='pearson'
    )

    best_expression, rank_ic, ic = factor_miner.run()
    end_time = time()

    print("\n========= 运行结果 =========")
    print("最佳因子表达式:", best_expression)
    print("Rank IC (Spearman):", rank_ic)
    print("IC (Pearson):", ic)
    print(f"运行时间: {end_time - start_time:.2f} 秒")
