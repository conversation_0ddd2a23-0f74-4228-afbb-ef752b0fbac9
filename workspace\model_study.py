import matplotlib.pyplot as plt
import statsmodels.api as sm
from sklearn.preprocessing import StandardScaler
import numpy as np
import pandas as pd

# 设置随机种子，保证可复现
np.random.seed(42)

# 生成 10,000 只股票的 5 个因子
num_samples = 10000
num_factors = 5

factor_names = [f'factor_{i+1}' for i in range(num_factors)]
factor_df = pd.DataFrame(np.random.randn(
    num_samples, num_factors), columns=factor_names)

# 生成未来 5 日收益率（目标变量）
factor_df['future_return'] = np.random.randn(num_samples) * 0.02  # 未来收益服从正态分布


scaler = StandardScaler()
factor_df[factor_names] = scaler.fit_transform(factor_df[factor_names])


# 添加截距项
X = sm.add_constant(factor_df[factor_names])
y = factor_df['future_return']

# 拟合 OLS 线性回归
model = sm.OLS(y, X).fit()

# 输出回归结果
print(model.summary())
factor_df['predicted_return'] = model.predict(X)
factor_df['rank'] = factor_df['predicted_return'].rank(pct=True)

factor_df['long_signal'] = factor_df['rank'] > 0.9  # 买入前 10% 股票
factor_df['short_signal'] = factor_df['rank'] < 0.1  # 卖出后 10% 股票
long_ret = factor_df[factor_df['long_signal']]['future_return'].mean()
short_ret = factor_df[factor_df['short_signal']]['future_return'].mean()

strategy_return = long_ret - short_ret
print(f"策略收益率: {strategy_return:.4f}")

plt.hist(factor_df['predicted_return'], bins=50, alpha=0.75, color='blue')
plt.axvline(long_ret, color='green', linestyle='dashed',
            linewidth=2, label="Long Mean Return")
plt.axvline(short_ret, color='red', linestyle='dashed',
            linewidth=2, label="Short Mean Return")
plt.legend()
plt.title("Distribution of strategic returns")
plt.show()
