# Traditional CTA 实时交易系统

## 概述

Traditional CTA 实时交易系统是一个基于 WebSocket 的实时交易信号生成系统，支持 MA 交叉策略和移动止盈止损功能。系统采用事件驱动架构，支持多种启动模式和灵活的持仓配置。

## 🚀 快速开始

### 超简单的使用方法

现在您只需要修改一个文件的顶部配置，就能轻松设置所有参数！

#### 使用步骤

1. **打开配置文件**: 用任何文本编辑器打开 `websocket_binance.py` 文件
2. **找到配置区域**: 在文件顶部找到配置区域（第14-58行）
3. **修改配置**: 根据您的需要修改配置项
4. **运行程序**: `python websocket_binance.py`

就这么简单！

## 📝 配置说明

### 配置区域位置

打开 `websocket_binance.py` 文件，在文件顶部（第14-58行）找到配置区域：

```python
# ============================================================================
# 配置区域 - 在这里修改您的设置
# ============================================================================
```

**重要**: 只修改这个配置区域的内容，不要修改其他代码部分。

### 配置项详解

#### 1. 启动模式配置

```python
STARTUP_MODE = "manual"  # 可选: "true", "false", "manual"
```

| 模式         | 参数值     | 说明                     |
| ------------ | ---------- | ------------------------ |
| 加载历史状态 | `"true"`   | 从状态文件加载之前的持仓 |
| 从零开始     | `"false"`  | 清空所有状态，从空仓开始 |
| 手动传入持仓 | `"manual"` | 使用配置的持仓数据启动   |

#### 2. 手动持仓配置

```python
MANUAL_POSITIONS = [
    {
        "direction": "long",      # "long" 或 "short"
        "price": 3500.0,         # 开仓价格
        "volume": 1.0,           # 持仓数量
        "datetime": "2024-01-15 10:00:00"  # 开仓时间(可选)
    },
    # 可以添加更多持仓
]
```

**字段说明：**
- `direction`: 必需，持仓方向，`"long"`(多头) 或 `"short"`(空头)
- `price`: 必需，开仓价格
- `volume`: 可选，持仓数量，默认1.0
- `datetime`: 可选，开仓时间，格式 `"YYYY-MM-DD HH:MM:SS"`，默认当前时间

#### 3. 策略参数配置

```python
STRATEGY_CONFIG = {
    "short_window": 5,           # 短期均线周期
    "long_window": 100,          # 长期均线周期
    "stop_loss_pct": 2.0,        # 止损百分比
    "profit_take_ratio": 0.8     # 回撤止盈比例
}
```

#### 4. 数据服务配置

```python
DATA_CONFIG = {
    "symbol": "ETHUSDT",         # 交易对
    "interval": "15m"            # K线周期
}
```

#### 5. 信号推送配置

```python
SIGNAL_CONFIG = {
    "title": "ETH@15min"         # 信号标题
}
```

## 🎯 常用配置场景

### 场景1: 设置一个多头持仓
```python
STARTUP_MODE = "manual"

MANUAL_POSITIONS = [
    {
        "direction": "long",
        "price": 3500.0,
        "volume": 1.0
    }
]
```

### 场景2: 设置多个持仓
```python
STARTUP_MODE = "manual"

MANUAL_POSITIONS = [
    {
        "direction": "long",
        "price": 3500.0,
        "volume": 1.0
    },
    {
        "direction": "short",
        "price": 3600.0,
        "volume": 0.5
    }
]
```

### 场景3: 从空仓开始
```python
STARTUP_MODE = "false"
```

### 场景4: 加载之前的持仓状态
```python
STARTUP_MODE = "true"
```

### 场景5: 修改策略参数
```python
STRATEGY_CONFIG = {
    "short_window": 10,          # 短期均线改为10
    "long_window": 50,           # 长期均线改为50
    "stop_loss_pct": 3.0,        # 止损改为3%
    "profit_take_ratio": 0.6     # 回撤止盈改为60%
}
```

### 场景6: 交易其他币种
```python
DATA_CONFIG = {
    "symbol": "BTCUSDT",         # 改为BTC
    "interval": "5m"             # 改为5分钟K线
}

SIGNAL_CONFIG = {
    "title": "BTC@5min"          # 对应修改标题
}
```

## 📋 配置项速查表

| 配置项                             | 说明         | 示例值                                  |
| ---------------------------------- | ------------ | --------------------------------------- |
| `STARTUP_MODE`                     | 启动模式     | `"manual"`, `"true"`, `"false"`         |
| `MANUAL_POSITIONS`                 | 手动持仓列表 | `[{"direction":"long","price":3500.0}]` |
| `STRATEGY_CONFIG["short_window"]`  | 短期均线周期 | `5`, `10`, `20`                         |
| `STRATEGY_CONFIG["long_window"]`   | 长期均线周期 | `50`, `100`, `200`                      |
| `STRATEGY_CONFIG["stop_loss_pct"]` | 止损百分比   | `2.0`, `3.0`, `5.0`                     |
| `DATA_CONFIG["symbol"]`            | 交易对       | `"ETHUSDT"`, `"BTCUSDT"`                |
| `DATA_CONFIG["interval"]`          | K线周期      | `"5m"`, `"15m"`, `"1h"`                 |

## 🔧 系统功能

### 自动功能

系统会自动为您：
- 计算止损价格（基于配置的止损百分比）
- 计算止盈价格（基于配置的止损百分比）
- 设置移动止盈（基于回撤比例）
- 验证数据格式
- 处理错误配置
- 保存和恢复持仓状态（防重复保存机制）

### 策略逻辑

- **MA交叉信号**: 短期均线上穿长期均线产生多头信号，下穿产生空头信号
- **移动止盈**: 根据价格变动动态调整止盈价格
- **固定止损**: 基于开仓价格和止损百分比设置固定止损
- **持仓管理**: 支持同时持有多个不同方向的持仓

### 数据流架构

1. **WebSocket接收**: 实时接收币安K线数据
2. **数据服务更新**: 更新K线面板数据
3. **策略计算**: 计算均线和交易信号
4. **信号输出**: 生成交易信号和持仓管理

## 🧪 测试和验证

### 测试配置
```bash
cd traditional_cta
python test_config.py
```

### 查看帮助
```bash
python websocket_binance.py --help
```

## ⚠️ 注意事项

1. **语法正确**: 确保Python语法正确，注意引号、逗号、括号
2. **时间格式**: 时间必须使用 `"YYYY-MM-DD HH:MM:SS"` 格式
3. **备份文件**: 修改前建议备份原文件
4. **只改配置**: 只修改配置区域，不要修改其他代码
5. **价格精度**: 价格建议使用4位小数

## 📊 日志输出示例

```
📊 启动模式: 手动设置持仓
✅ 使用配置的持仓数据: 2个持仓
📊 选择手动传入持仓模式
✅ 创建持仓1: 📈 long @ 3500.0000 (数量: 1.0)
✅ 创建持仓2: 📉 short @ 3600.0000 (数量: 0.5)
📝 设置手动输入持仓: 2个持仓(1个多头, 1个空头)
   持仓1: 📈 long @ 3500.0000 (数量: 1.0, 开仓时间: 2024-01-15 10:00:00)
           止损价: 3430.0000 | 止盈价: 3570.0000
   持仓2: 📉 short @ 3600.0000 (数量: 0.5, 开仓时间: 2025-05-27 16:15:15)
           止损价: 3672.0000 | 止盈价: 3528.0000
```

## 🆘 故障排除

### 常见问题

**Q: 如何修改持仓？**
A: 修改文件顶部配置区域的 `MANUAL_POSITIONS` 列表。

**Q: 如何更换交易对？**
A: 修改 `DATA_CONFIG["symbol"]` 和对应的 `SIGNAL_CONFIG["title"]`。

**Q: 如何调整策略参数？**
A: 修改 `STRATEGY_CONFIG` 中的相应参数。

**Q: 遇到语法错误怎么办？**
A: 检查配置区域的Python语法，确保引号、逗号、括号正确。

### 错误处理

- JSON格式错误 → 系统报错并跳过无效数据
- 缺少必需字段 → 该持仓被跳过
- 无效方向 → 该持仓被跳过
- 时间格式错误 → 使用当前时间

## 🎉 一分钟快速设置

想要快速开始？复制以下配置到文件顶部：

```python
# 启动模式配置
STARTUP_MODE = "manual"  # 手动设置持仓

# 手动持仓配置
MANUAL_POSITIONS = [
    {
        "direction": "long",      # 多头持仓
        "price": 3500.0,         # 开仓价格
        "volume": 1.0            # 持仓数量
    }
]

# 策略参数配置 (可选修改)
STRATEGY_CONFIG = {
    "short_window": 5,           # MA5
    "long_window": 100,          # MA100
    "stop_loss_pct": 2.0,        # 2%止损
    "profit_take_ratio": 0.8     # 80%回撤止盈
}

# 数据服务配置 (可选修改)
DATA_CONFIG = {
    "symbol": "ETHUSDT",         # ETH交易对
    "interval": "15m"            # 15分钟K线
}

# 信号推送配置 (可选修改)
SIGNAL_CONFIG = {
    "title": "ETH@15min"         # 信号标题
}
```

保存文件，运行 `python websocket_binance.py`，就开始了！

---

**现在配置变得超级简单，只需要修改文件顶部的几行代码就能完成所有设置！** 🎉
