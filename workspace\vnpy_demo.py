# 数据转换 + 回测执行
import pandas as pd
from vnpy_ctastrategy import BacktestingEngine, CtaTemplate
from vnpy.trader.object import BarData

# 生成示例数据
df = pd.DataFrame({
    "open": [100, 101, 102],
    "high": [105, 106, 103],
    "low": [99, 100, 101],
    "close": [103, 105, 102],
    "volume": [1000, 2000, 1500],
    "signals": [1, 0, -1]
})
df.index = pd.date_range(start="2023-01-01", periods=3, freq="D")

# 转换为BarData列表
bars = []
for dt, row in df.iterrows():
    bars.append(BarData(
        symbol="BTCUSDT",
        exchange="BINANCE",
        datetime=dt.to_pydatetime(),
        interval="1d",
        open_price=row["open"],
        high_price=row["high"],
        low_price=row["low"],
        close_price=row["close"],
        volume=row["volume"],
        gateway_name=f"signal={row['signals']}"  # 传递信号
    ))

# 运行回测
engine = BacktestingEngine()
engine.set_parameters(
    vt_symbol="BTCUSDT.BINANCE",
    interval="1d",
    start=df.index[0],
    end=df.index[-1],
    capital=1_000_000
)
engine.history_data = bars


class DemoStrategy(CtaTemplate):
    def on_bar(self, bar: BarData):
        signal = int(bar.gateway_name.split("=")[1])
        if signal == 1 and not self.pos:
            self.buy(bar.close_price, 1)
        elif signal == -1 and not self.pos:
            self.short(bar.close_price, 1)


engine.add_strategy(DemoStrategy, {})
engine.run_backtesting()
print(engine.trades)  # 查看交易记录
