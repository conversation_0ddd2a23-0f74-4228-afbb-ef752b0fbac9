import json

import pandas as pd
import data_handler
import pandas as pd
from datetime import datetime
import plotly.graph_objects as go
import numpy as np
import time
import pickle


class BackTester:
    def __init__(self,
                 data_handler,
                 execution_handler,
                 performance_metrics,
                 visualizer,
                 config):
        """
        初始化回测类
        :param data_handler: 数据处理类，用于获取数据并划分训练集、验证集、测试集
        :param execution_handler: 执行处理类，负责交易执行及仓位管理
        :param performance_metrics: 性能评估类，计算回测指标并打印结果
        :param visualizer: 可视化类，用于绘制回测结果
        :param config: 配置字典，包含回测相关的配置参数，如初始资金等
        """

        self.data_handler = data_handler
        self.execution_handler = execution_handler
        self.performance_metrics = performance_metrics
        self.visualizer = visualizer
        self.config = config

        self.exercise = []
        self.verification = []
        self.test = []
        return

    def run_backtest(self):
        """
        执行回测
        该方法负责整体回测流程的执行，
        包括数据划分、训练集、验证集、测试集回测，以及回测结果的评估与可视化。

        :param None: 无
        :return: None
        """
        # 数据清洗
        self.data_handler.clean_data()

        # 数据划分
        train_data, valid_data, test_data = self.data_handler.split_data()

        # 回测训练集
        print("Running backtest on the training set:")
        self.run_single_backtest_lack(train_data)

        # 回测验证集
        print("Running backtest on the validation set:")
        self.run_single_backtest_lack(valid_data)

        # 回测测试集
        # with open('test_data_lack.json', 'w', encoding='utf-8') as f:
        #     json.dump( test_data.to_dict(), f, ensure_ascii=False, indent=4)
        print("Running backtest on the test set:")
        self.run_single_backtest_lack(test_data, True)

    def run_single_backtest_lack(self, data, isw=False):
        """
        执行单次回测
        该方法负责对某一数据集进行回测，计算账户净值和每日收益。

        :param data: 回测数据集（训练集、验证集或测试集）
        :type data: pandas.DataFrame
        :return: None
        """
        start_time = time.time()

        # 初始化初始资金
        initial_capital = self.config['initial_capital']

        # 准备净值曲线和每日收益的初始状态
        equity_curve_transitory = []
        # 遍历数据集，获取每天的交易信号
        filtered_data = data.copy()
        filtered_data.reset_index(drop=True, inplace=True)

        balance_index_0 = (
                self.execution_handler.position['available_cash'] +
                abs(self.execution_handler.position['shares'] * self.execution_handler.position['avg_cost'])
        )
        close_time_index_0 = filtered_data.close_time[0]
        filtered_data = filtered_data[filtered_data['signals'] != 0]

        for index, row in filtered_data.iterrows():
            price = row['close']  # 获取当天价格
            signal = row['signals']  # 获取交易信号
            close_time = row['close_time']

            # 使用交易执行处理器执行交易
            volume = 100  # 假设默认每次交易100个单位，可以改为你的交易策略决定的量
            self.execution_handler.execute_trade(signal, price, volume, close_time)

            # 更新净值曲线
            balance = (
                    self.execution_handler.position['available_cash'] +
                    abs(self.execution_handler.position['shares'] * self.execution_handler.position['avg_cost'])
            )

            equity_curve_transitory.append([close_time, round(float(balance), 4)])

        time_val = pd.DataFrame(equity_curve_transitory, columns=['close_time', 'val'])
        # time_val['close_time'] = time_val['time'].apply(convert_milliseconds_to_datetime)
        # equity_curve = time_val.set_index('close_time')['val']

        # 补数据
        a = pd.Series('0', index=data.close_time)
        b = time_val.set_index('close_time')['val'].astype('str')
        if len(b) < len(a):
            a.update(b)
            if a[close_time_index_0] == '0':
                a[close_time_index_0] = balance_index_0
            a_replaced = a.replace('0', pd.NA)
            equity_curve = a_replaced.ffill()
            equity_curve = equity_curve.astype('float64')
        else:
            equity_curve = time_val.set_index('close_time')['val'].astype('float64')

        # 判断是否有索引 1
        if close_time_index_0 not in equity_curve.index:
            # 如果没有索引 1，新增索引 1，值可以根据需求设定（这里设为 0）
            equity_curve = pd.concat(
                [pd.Series([balance_index_0], index=[close_time_index_0]), equity_curve]).sort_index()

        end_time = time.time()
        execution_time = end_time - start_time
        print(f"代码块执行时间: {execution_time:.4f} 秒，数量：{len(equity_curve)}")
        # print(f"：{equity_curve}")

        # if isw == True:
        #     with open('test_data_lack.json', 'w', encoding='utf-8') as f:
        #         json.dump(data.to_dict(), f, ensure_ascii=False, indent=4)
        #
        #     print("---------------记录数据-------")
        #     equity_dict = equity_curve.to_dict()
        #     with open('lack.json', 'w', encoding='utf-8') as f:
        #         json.dump(equity_dict, f, ensure_ascii=False, indent=4)

        # 计算每日收益
        daily_returns = equity_curve.pct_change().dropna()

        # 评估性能
        self.performance_metrics.calculate(equity_curve)
        self.performance_metrics.print_results()

        # 可视化
        self.visualizer.plot_equity_curve(equity_curve)
        self.visualizer.plot_drawdown(equity_curve)
        self.visualizer.plot_daily_returns(daily_returns)
        self.visualizer.plot_return_distribution(daily_returns)

        # self.execution_handler.print_trade_log()


# 定义一个函数，将毫秒转换为日期时间字符串
def convert_milliseconds_to_datetime(ms):
    # 将毫秒转换为秒
    seconds = ms / 1000.0
    # 转换为 datetime 对象
    dt = datetime.fromtimestamp(seconds)
    # 格式化为 'YYYY-MM-DD HH:MM:SS' 字符串
    return dt.strftime('%Y-%m-%d %H:%M:%S')
