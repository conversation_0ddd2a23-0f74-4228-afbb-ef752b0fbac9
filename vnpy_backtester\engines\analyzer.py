"""
Analyzer for backtesting results
"""

from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import pandas as pd
import numpy as np

from vnpy_backtester.objects.object import TradeData
from vnpy_backtester.utils.base import DailyResult


class BacktestingAnalyzer:
    """
    Analyzer for backtesting results.
    """

    def __init__(self) -> None:
        """
        Initialize analyzer.
        """
        self.trades: List[TradeData] = []
        self.daily_results: Dict[datetime, DailyResult] = {}

    def set_trades(self, trades: List[TradeData]) -> None:
        """
        Set trade data.
        """
        self.trades = trades

    def set_daily_results(self, daily_results: Dict[datetime, DailyResult]) -> None:
        """
        Set daily results.
        """
        self.daily_results = daily_results

    def analyze_trades(self) -> Dict[str, Any]:
        """
        Analyze trade data.
        """
        if not self.trades:
            return {}

        # Convert trades to DataFrame
        trade_data = []
        for trade in self.trades:
            trade_data.append({
                "datetime": trade.datetime,
                "direction": trade.direction.value,
                "offset": trade.offset.value,
                "price": trade.price,
                "volume": trade.volume,
                "vt_symbol": trade.vt_symbol
            })

        df = pd.DataFrame(trade_data)

        # Calculate trade statistics
        total_trades = len(df)

        long_trades = df[df["direction"] == "多"]
        short_trades = df[df["direction"] == "空"]

        open_trades = df[df["offset"] == "开"]
        close_trades = df[df["offset"] == "平"]

        total_volume = df["volume"].sum()
        total_turnover = (df["price"] * df["volume"]).sum()

        # Group by symbol
        symbol_stats = df.groupby("vt_symbol").agg({
            "volume": "sum",
            "price": lambda x: (x * df.loc[x.index, "volume"]).sum() / df.loc[x.index, "volume"].sum()
        }).reset_index()

        symbol_stats.columns = ["vt_symbol", "volume", "avg_price"]
        symbol_stats["turnover"] = symbol_stats["volume"] * \
            symbol_stats["avg_price"]

        # Calculate holding time
        holding_times = []

        for vt_symbol in df["vt_symbol"].unique():
            symbol_df = df[df["vt_symbol"] == vt_symbol]

            for direction in ["多", "空"]:
                direction_df = symbol_df[symbol_df["direction"] == direction]

                open_trades = direction_df[direction_df["offset"] == "开"]
                close_trades = direction_df[direction_df["offset"] == "平"]

                for _, open_trade in open_trades.iterrows():
                    open_time = open_trade["datetime"]

                    for _, close_trade in close_trades.iterrows():
                        close_time = close_trade["datetime"]

                        if close_time > open_time:
                            holding_time = (
                                # hours
                                close_time - open_time).total_seconds() / 3600
                            holding_times.append(holding_time)
                            break

        avg_holding_time = np.mean(holding_times) if holding_times else 0
        max_holding_time = np.max(holding_times) if holding_times else 0
        min_holding_time = np.min(holding_times) if holding_times else 0

        return {
            "total_trades": total_trades,
            "long_trades": len(long_trades),
            "short_trades": len(short_trades),
            "open_trades": len(open_trades),
            "close_trades": len(close_trades),
            "total_volume": total_volume,
            "total_turnover": total_turnover,
            "avg_holding_time": avg_holding_time,
            "max_holding_time": max_holding_time,
            "min_holding_time": min_holding_time,
            "symbol_stats": symbol_stats
        }

    def analyze_daily(self) -> Dict[str, Any]:
        """
        Analyze daily results.
        """
        if not self.daily_results:
            return {}

        # Convert daily results to DataFrame
        daily_data = []
        for date, result in self.daily_results.items():
            daily_data.append({
                "date": date,
                "close_price": result.close_price,
                "pre_close": result.pre_close,
                "trades": result.trades,
                "turnover": result.turnover,
                "commission": result.commission,
                "slippage": result.slippage,
                "trading_pnl": result.trading_pnl,
                "holding_pnl": result.holding_pnl,
                "total_pnl": result.total_pnl,
                "net_pnl": result.net_pnl
            })

        df = pd.DataFrame(daily_data)

        # Calculate daily statistics
        total_days = len(df)
        profit_days = len(df[df["net_pnl"] > 0])
        loss_days = len(df[df["net_pnl"] < 0])

        max_profit_day = df.loc[df["net_pnl"].idxmax()]
        max_loss_day = df.loc[df["net_pnl"].idxmin()]

        avg_profit = df[df["net_pnl"] >
                        0]["net_pnl"].mean() if profit_days else 0
        avg_loss = df[df["net_pnl"] < 0]["net_pnl"].mean() if loss_days else 0

        total_commission = df["commission"].sum()
        total_slippage = df["slippage"].sum()
        total_turnover = df["turnover"].sum()
        total_trading_pnl = df["trading_pnl"].sum()
        total_holding_pnl = df["holding_pnl"].sum()
        total_pnl = df["total_pnl"].sum()
        total_net_pnl = df["net_pnl"].sum()

        return {
            "total_days": total_days,
            "profit_days": profit_days,
            "loss_days": loss_days,
            "profit_loss_ratio": profit_days / loss_days if loss_days else float("inf"),
            "max_profit_day": max_profit_day["date"],
            "max_profit": max_profit_day["net_pnl"],
            "max_loss_day": max_loss_day["date"],
            "max_loss": max_loss_day["net_pnl"],
            "avg_profit": avg_profit,
            "avg_loss": avg_loss,
            "avg_profit_loss_ratio": abs(avg_profit / avg_loss) if avg_loss else float("inf"),
            "total_commission": total_commission,
            "total_slippage": total_slippage,
            "total_turnover": total_turnover,
            "total_trading_pnl": total_trading_pnl,
            "total_holding_pnl": total_holding_pnl,
            "total_pnl": total_pnl,
            "total_net_pnl": total_net_pnl
        }

    def calculate_drawdown(self) -> Dict[str, Any]:
        """
        Calculate drawdown related statistics.
        """
        if not self.daily_results:
            return {}

        # Calculate balance curve
        balance = []
        drawdown = []
        drawdown_percent = []

        init_balance = 0
        max_balance = 0

        for date, result in sorted(self.daily_results.items()):
            if not balance:
                init_balance = result.net_pnl
                balance.append(init_balance)
                max_balance = init_balance
            else:
                balance.append(balance[-1] + result.net_pnl)
                max_balance = max(max_balance, balance[-1])

            dd = max_balance - balance[-1]
            drawdown.append(dd)
            drawdown_percent.append(
                dd / max_balance * 100 if max_balance else 0)

        max_drawdown = max(drawdown)
        max_drawdown_percent = max(drawdown_percent)

        # Find max drawdown period
        max_dd_end = drawdown.index(max_drawdown)
        max_dd_start = max_dd_end

        while max_dd_start > 0:
            if balance[max_dd_start - 1] >= balance[max_dd_end]:
                break
            max_dd_start -= 1

        max_dd_duration = max_dd_end - max_dd_start

        # Find underwater periods
        underwater_periods = []
        current_period = []

        for i, dd in enumerate(drawdown):
            if dd == 0:
                if current_period:
                    underwater_periods.append(current_period)
                    current_period = []
            else:
                current_period.append(i)

        if current_period:
            underwater_periods.append(current_period)

        avg_underwater_duration = np.mean(
            [len(p) for p in underwater_periods]) if underwater_periods else 0
        max_underwater_duration = np.max(
            [len(p) for p in underwater_periods]) if underwater_periods else 0

        return {
            "max_drawdown": max_drawdown,
            "max_drawdown_percent": max_drawdown_percent,
            "max_drawdown_duration": max_dd_duration,
            "avg_underwater_duration": avg_underwater_duration,
            "max_underwater_duration": max_underwater_duration
        }

    def calculate_statistics(self) -> Dict[str, Any]:
        """
        Calculate all statistics.
        """
        # Combine all analysis results
        trade_analysis = self.analyze_trades()
        daily_analysis = self.analyze_daily()
        drawdown_analysis = self.calculate_drawdown()

        statistics = {}
        statistics.update(trade_analysis)
        statistics.update(daily_analysis)
        statistics.update(drawdown_analysis)

        return statistics
