import pandas as pd
import numpy as np
from typing import Optional, Union


class QuickBacktest:
    """
    增强版回测引擎，实时计算每次交易的资金缺口

    参数:
        data (pd.DataFrame): 包含价格、持仓和交易成本数据的DataFrame
        price_col (str): 价格列名，默认为'close'
        position_col (str): 持仓信号列名，默认为'position'
        fee (Union[str, float]): 手续费（费率或固定费用）
        slippage (Union[str, float]): 滑点（单位价格滑点）
    """

    def __init__(self,
                 data: pd.DataFrame,
                 price_col: str = 'close',
                 position_col: str = 'position',
                 fee: Union[str, float] = 'fee',
                 slippage: Union[str, float] = 'slippage'):

        self.data = data.copy()
        self.price_col = price_col
        self.position_col = position_col
        self.fee = fee
        self.slippage = slippage
        self.results = None

        self._validate_inputs()

    def _validate_inputs(self):
        required_cols = [self.price_col, self.position_col]
        if isinstance(self.fee, str):
            required_cols.append(self.fee)
        if isinstance(self.slippage, str):
            required_cols.append(self.slippage)
        missing = [col for col in required_cols if col not in self.data]
        if missing:
            raise ValueError(f"缺失字段: {missing}")

    def run_backtest(self, initial_capital: float = 1.0) -> pd.DataFrame:
        df = self.data.copy()

        # 预处理仓位数据
        df[self.position_col] = df[self.position_col].ffill().fillna(0)

        # 计算仓位变化
        df['position_chg'] = df[self.position_col].diff().fillna(0)

        # 计算交易成本
        fee = df[self.fee] if isinstance(self.fee, str) else self.fee
        slippage = df[self.slippage] if isinstance(
            self.slippage, str) else self.slippage

        # 成本计算逻辑
        df['transaction_cost'] = (
            abs(df['position_chg']) * df[self.price_col] * fee  # 比例手续费
            + abs(df['position_chg']) * slippage  # 滑点成本
            + (df['position_chg'] != 0) *
            fee if isinstance(fee, (int, float)) else 0  # 固定手续费
        )

        # 计算收益
        df['price_change'] = df[self.price_col].diff().fillna(0)
        df['holding_pnl'] = df[self.position_col] * df['price_change']
        df['daily_pnl'] = df['holding_pnl'] - df['transaction_cost']

        # 构建资金曲线
        df['cumulative_pnl'] = df['daily_pnl'].cumsum()
        df['equity_curve'] = initial_capital + df['cumulative_pnl']
        df['cash'] = df['equity_curve'] - (df['position'] * df[self.price_col])

        # 计算资金缺口（核心改进点）
        df['prev_equity'] = df['equity_curve'].shift(1).fillna(initial_capital)
        # df['capital_shortage'] = np.maximum(df['transaction_cost'] - df['prev_equity'], 0)

        # 标记有效交易日
        df['trading_day'] = df['position_chg'] != 0

        self.results = df
        return self.results
