import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
# from iid import TimeSeriesHomogeneityTest
from statsmodels.tsa.stattools import adfuller
from data_service import DataService
from group_stats import GroupStats
from factor_dist import analyze_factor_distribution


def factor_evaluator(data):
    """
    原始因子评估函数，接受包含因子和收益率的DataFrame

    参数:
        data (pd.DataFrame): 包含'signals'和'r96'列的DataFrame

    返回:
        dict: 包含分析结果的字典
    """
    result = {}

    df = data.dropna()
    # ========平稳性检验=============================================================#

    # 进行 ADF 单位根检验
    adf_result = adfuller(df['signals'], autolag='AIC')
    p_value = adf_result[1]

    # 设置判断同质性的 p 值阈值 (例如 0.05)
    # print("="*40)
    # print(f'p_value : {p_value}')
    result['p_value'] = p_value
    # print("="*40)

    # ========pearson相关系数计算=============================================================#
    ic = df['signals'].corr(df['r96'], method='pearson')
    # print(f'IC : {ic}')
    result['IC'] = ic
    # print("="*40)

    # ========spearman相关系数计算=============================================================#
    rank_ic = df['signals'].corr(df['r96'], method='spearman')
    # print(f'Rank_IC : {rank_ic}')
    result['Rank_IC'] = rank_ic
    # print("="*40)

    # ========IR计算（10组）=============================================================#
    n = 10
    group_labels = pd.qcut(df['signals'], n, labels=False, duplicates='drop')
    grouped = df.groupby(group_labels)[['signals', 'r96']]

    pearson_corrs_pd = []
    for _, group in grouped:
        corr_matrix = group.corr()
        pearson_corrs_pd.append(corr_matrix.iloc[0, 1])

    ir_value_pd = np.mean(pearson_corrs_pd) / np.std(pearson_corrs_pd)
    # print(f'IR : {ir_value_pd}')
    result['IR'] = ir_value_pd
    # print("="*40)

    # ========分组收益分析计算=============================================================#
    analyzer = GroupStats(df, value_col='signals', return_col='r96')
    stats, fig = analyzer.analyze_groups(n_groups=20, show_plot=False)
    result['stats'] = stats
    result['fig'] = fig
    # print(stats)
    # print("="*40)

    # ========因子分布情况=============================================================#
    # result = analyze_factor_distribution(df)

    # n = 10  # 总窗口数
    # for window_id in range(n):
    #     print(f"\n窗口 {window_id+1}/{n}")
    #     print("="*40)
    #     print("时间范围:", result['window_stats'][n]['time_ranges'][window_id])

    #     # 获取当前窗口的统计指标
    #     stats = result['window_stats'][n]['stats'].iloc[window_id]

    #     # 格式化打印每个指标
    #     print("\n统计指标:")
    #     for metric, value in stats.items():
    #         print(f"{metric:<10}: {value:>10.4f}")  # 左对齐指标名，右对齐数值

    #     print("="*40)

    return result


class FactorEvaluator:
    """
    因子评估工具类，支持类似DataService的调用方式

    用法示例:
    fe = FactorEvaluator()
    result = fe['ETHUSDT_15m_2020_2025', '2021-10-1', '2022-10-1']  # 币种, 开始日期, 结束日期
    result = fe['ETHUSDT_15m_2020_2025', '2021-10-1']  # 币种, 开始日期, 结束日期=None
    result = fe['ETHUSDT_15m_2020_2025']  # 币种, 开始日期=None, 结束日期=None
    """

    def __init__(self, return_period=96, show_plots=False):
        """
        初始化因子评估器

        参数:
            return_period (int): 收益率计算周期，默认为96（对应15分钟K线的24小时）
            show_plots (bool): 是否显示图表，默认为False
        """
        self.return_period = return_period
        self.show_plots = show_plots
        self.ds = DataService()
        self.df = None
        self.currency = None
        self.start_date = None
        self.end_date = None

    def __getitem__(self, key):
        """
        支持类似DataService的调用方式

        参数:
            key: 可以是以下格式:
                - str: 仅币种代码
                - tuple: (币种代码, 开始日期)
                - tuple: (币种代码, 开始日期, 结束日期)

        返回:
            dict: 分析结果
        """
        # 解析参数
        if isinstance(key, str):
            # 只提供了币种
            currency = key
            start_date = None
            end_date = None
        elif isinstance(key, tuple):
            if len(key) == 2:
                # 提供了币种和开始日期
                currency, start_date = key
                end_date = None
            elif len(key) == 3:
                # 提供了币种、开始日期和结束日期
                currency, start_date, end_date = key
            else:
                raise ValueError(
                    "参数格式不正确，应为 (币种) 或 (币种, 开始日期) 或 (币种, 开始日期, 结束日期)")
        else:
            raise ValueError("参数格式不正确，应为币种字符串或包含币种和日期的元组")

        # 加载数据并运行分析
        self.load_data(currency, start_date, end_date)
        return self.run_analysis(self.show_plots)

    def load_data(self, currency=None, start_date=None, end_date=None, return_period=None):
        """
        加载并准备数据

        参数:
            currency (str): 币种代码，如果不提供则使用初始化时的值
            start_date (str): 起始日期，如果不提供则使用初始化时的值
            end_date (str): 结束日期，如果不提供则使用初始化时的值
            return_period (int): 收益率计算周期，如果不提供则使用初始化时的值
        """
        # 更新参数（如果提供了新值）
        if currency is not None:
            self.currency = currency
        if start_date is not None:
            self.start_date = start_date
        if end_date is not None:
            self.end_date = end_date
        if return_period is not None:
            self.return_period = return_period

        # 检查必要参数
        if self.currency is None:
            raise ValueError("必须提供币种代码")

        # 加载K线数据
        df = self.ds[self.currency]

        # 应用日期过滤
        if self.start_date:
            df = df[self.start_date:]
        if self.end_date:
            df = df[:self.end_date]

        # 添加默认因子数据（均线偏离）
        df['signals'] = df['close'] - df['close'].rolling(20).mean()

        # 计算未来收益率
        df[f'r{self.return_period}'] = df['close'].shift(
            -self.return_period) / df['close'] - 1

        # 清理缺失值
        df = df.dropna()

        self.df = df
        return df

    def run_analysis(self, show_plots=False):
        """
        运行与原main.py相同的分析流程

        参数:
            show_plots (bool): 是否显示图表

        返回:
            dict: 包含分析结果的字典
        """
        if self.df is None:
            self.load_data()

        return_col = f'r{self.return_period}'

        # ========平稳性检验=============================================================#
        adf_result = adfuller(self.df['signals'], autolag='AIC')
        p_value = adf_result[1]

        # ========pearson相关系数计算=============================================================#
        ic = self.df['signals'].corr(self.df[return_col], method='pearson')

        # ========spearman相关系数计算=============================================================#
        rank_ic = self.df['signals'].corr(
            self.df[return_col], method='spearman')

        # ========IR计算（10组）=============================================================#
        n = 10
        group_labels = pd.qcut(
            self.df['signals'], n, labels=False, duplicates='drop')
        grouped = self.df.groupby(group_labels)[['signals', return_col]]

        pearson_corrs_pd = []
        for _, group in grouped:
            corr_matrix = group.corr()
            pearson_corrs_pd.append(corr_matrix.iloc[0, 1])

        ir_value_pd = np.mean(pearson_corrs_pd) / np.std(pearson_corrs_pd)

        # ========分组收益分析计算=============================================================#
        analyzer = GroupStats(
            self.df, value_col='signals', return_col=return_col)
        stats, fig = analyzer.analyze_groups(n_groups=20, show_plot=show_plots)

        # ========因子分布情况=============================================================#
        dist_result = analyze_factor_distribution(self.df)

        # 显示因子分布直方图
        if show_plots:
            self.df['signals'].hist(bins=100)
            plt.show()

        # 将所有结果整合到一个字典中
        analysis_result = {
            'adf_p_value': p_value,
            'ic': ic,
            'rank_ic': rank_ic,
            'ir': ir_value_pd,
            'group_stats': stats,
            'group_fig': fig,
            'distribution': dist_result,
            'df': self.df  # 包含原始数据，方便后续分析
        }

        return analysis_result
