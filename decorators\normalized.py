import functools
import pandas as pd


def normalize():
    '''装饰器，对信号进行逐点标准化'''
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            signals = func(*args, **kwargs)  # 调用原函数获取 Series

            # 计算逐点均值和标准差
            expanding_mean = signals.expanding(min_periods=1).mean()
            expanding_std = signals.expanding(min_periods=1).std()

            # 逐点标准化
            normalized_signal = (signals - expanding_mean) / expanding_std

            return normalized_signal

        return wrapper
    return decorator
