"""
信号追踪止损策略 - 根据signals列的值进行交易，使用移动止损出场
结合多仓位策略的开仓条件和MA交叉追踪止损策略的出场条件
"""

from typing import List, Tuple
from datetime import datetime
from vnpy_backtester.templates.template import StrategyTemplate
from vnpy_backtester.objects.object import BarData
from vnpy_backtester.utils.log_engine import log_engine


class Position:
    """
    持仓类 - 用于记录每个持仓的信息（包含止损止盈功能）
    """

    def __init__(self, direction: str, price: float, volume: float, entry_time: datetime):
        """
        初始化持仓

        参数:
        direction: 方向，"long"或"short"
        price: 开仓价格
        volume: 开仓数量
        entry_time: 开仓时间
        """
        self.direction = direction  # "long"或"short"
        self.price = price  # 开仓价格
        self.volume = volume  # 开仓数量
        self.entry_time = entry_time  # 开仓时间

        # 止损止盈相关属性
        self.stop_loss_price = 0.0  # 止损价格
        self.take_profit_price = 0.0  # 止盈价格
        self.highest_price = price  # 记录最高价（多头）或最低价（空头）


class SignalTrailingStopStrategy(StrategyTemplate):
    """
    信号追踪止损策略

    开仓条件：
    - 根据signals列的值进行交易
    - 当signals=1时开多
    - 当signals=-1时开空

    出场条件：
    - 使用移动止损机制
    - 每次开仓后，设置止盈止损线
    - 当跌破止损线后，立即止损
    - 涨破止盈线后，不止盈，记录K线最高价和当前止盈价
    - 当利润回撤到最高价和止盈线之间指定比例时，进行止盈
    """

    # 策略参数
    position_size = 1  # 每次开仓的数量或金额
    size = 1  # 合约乘数
    commission_rate = 0.0003  # 默认手续费率，将从引擎获取
    slippage_rate = 0.01  # 默认滑点率，将从引擎获取
    order_type = "quantity"  # 下单方式，可选"quantity"(按币数量)或"amount"(按金额)

    # 移动止损参数
    stop_loss_pct = 3.0  # 止损百分比，默认为3%
    profit_take_ratio = 0.6  # 回撤比例，当利润回撤到60%时止盈

    # 策略变量
    positions = []  # 持仓列表，每个元素是一个Position对象

    def __init__(self, engine, strategy_name: str, vt_symbol: str, setting: dict = None):
        """
        初始化策略
        """
        super().__init__(engine, strategy_name, vt_symbol, setting)

        # 从设置中获取参数
        if setting:
            # 基础参数
            self.position_size = setting.get(
                "position_size", self.position_size)
            self.order_type = setting.get("order_type", self.order_type)

            # 移动止损参数
            self.stop_loss_pct = setting.get(
                "stop_loss_pct", self.stop_loss_pct)
            self.profit_take_ratio = setting.get(
                "profit_take_ratio", self.profit_take_ratio)

        # 初始化持仓列表
        self.positions = []

        # 资金管理相关变量
        self._used_capital = 0.0  # 已使用资金
        self._cached_available_capital = 0.0  # 缓存的可用资金
        self._last_bar_datetime = None  # 上次计算资金的K线时间

        # 保存当前K线对象，供其他方法使用
        self.bar = None

    def get_available_capital(self, force_recalculate=False):
        """
        计算可用资金

        参数:
        force_recalculate: 是否强制重新计算，默认为False

        返回:
        float: 可用资金
        """
        # 如果不是强制重新计算，且缓存有效，则返回缓存值
        if not force_recalculate and self._last_bar_datetime == self.bar.datetime:
            return self._cached_available_capital

        # 从引擎获取初始资金
        initial_capital = 50000.0  # 默认值
        if hasattr(self, "engine") and hasattr(self.engine, "capital"):
            initial_capital = self.engine.capital

        # 使用累计方式计算已使用资金，而不是每次都重新计算
        if not hasattr(self, '_used_capital'):
            # 首次计算
            self._used_capital = 0.0
            for position in self.positions:
                self._used_capital += position.price * position.volume

        # 计算可用资金
        available_capital = initial_capital - self._used_capital

        # 确保可用资金不小于0
        available_capital = max(0.0, available_capital)

        # 缓存计算结果
        self._last_bar_datetime = self.bar.datetime
        self._cached_available_capital = available_capital

        return available_capital

    def write_log(self, msg, level="INFO"):
        """
        写入日志

        参数:
        msg: 日志消息
        level: 日志级别，可以是"DEBUG", "INFO", "WARNING", "ERROR"
        """
        # 只有在非DEBUG级别或者DEBUG模式开启时才记录日志
        if level != "DEBUG" or getattr(self, "debug_mode", False):
            # 使用日志引擎写入日志
            log_engine.write_log(f"{self.strategy_name} - {msg}")

            # 同时调用父类的write_log方法
            super().write_log(msg)

    def on_init(self):
        """
        策略初始化
        """
        self.write_log("信号追踪止损策略初始化")

        # 从引擎获取手续费率和滑点率
        if hasattr(self, "engine"):
            self.commission_rate = self.engine.rate
            self.slippage_rate = self.engine.slippage
            self.size = self.engine.size
            self.write_log(
                f"从引擎获取参数 - 手续费率: {self.commission_rate}, 滑点率: {self.slippage_rate}, 合约乘数: {self.size}")

    def on_start(self):
        """
        策略启动
        """
        self.write_log("信号追踪止损策略启动")

    def on_stop(self):
        """
        策略停止
        """
        self.write_log("信号追踪止损策略停止")

    def check_stop_loss_take_profit(self, bar: BarData):
        """
        检查止损止盈条件

        参数:
        bar: 当前K线数据

        返回:
        List[Tuple[Position, str, float]]: 需要平仓的持仓列表，每个元素包含(持仓对象, 平仓原因, 平仓价格)
        """
        positions_to_close = []

        for position in self.positions:
            # 更新持仓的最高/最低价格
            if position.direction == "long":
                # 多头持仓，更新最高价
                if bar.high_price > position.highest_price:
                    position.highest_price = bar.high_price
                    # 更新止盈价格 (开仓价格 + 止损百分比)
                    position.take_profit_price = position.price * \
                        (1 + self.stop_loss_pct / 100)
                    # 调试信息，可以选择性记录
                    # self.write_log(f"多头更新最高价: {position.highest_price:.4f}, 止盈价: {position.take_profit_price:.4f}")

                # 检查是否触发止损
                stop_loss_price = position.price * \
                    (1 - self.stop_loss_pct / 100)
                if bar.close_price <= stop_loss_price:
                    # 触发止损
                    positions_to_close.append(
                        (position, "止损", bar.close_price))
                    self.write_log(
                        f"多头触发止损: 当前收盘价 {bar.close_price:.4f} <= 止损价 {stop_loss_price:.4f}")
                    continue

                # 检查是否触发移动止盈
                # 只有当价格曾经超过止盈线，且当前回撤到指定比例时才触发
                if position.highest_price > position.take_profit_price:
                    # 计算回撤价格 = 止盈价 + (最高价 - 止盈价) * (1 - 回撤比例)
                    retrace_price = position.take_profit_price + \
                        (position.highest_price -
                         position.take_profit_price) * (1 - self.profit_take_ratio)
                    # 使用收盘价与回撤价比较，而不是最低价
                    if bar.close_price <= retrace_price:
                        # 触发移动止盈
                        positions_to_close.append(
                            (position, "移动止盈", bar.close_price))
                        self.write_log(
                            f"多头触发移动止盈: 当前收盘价 {bar.close_price:.4f} <= 回撤价 {retrace_price:.4f} (最高价: {position.highest_price:.4f}, 止盈价: {position.take_profit_price:.4f})")
                        continue

            elif position.direction == "short":
                # 空头持仓，更新最低价（用highest_price字段存储最低价）
                if bar.low_price < position.highest_price:
                    position.highest_price = bar.low_price
                    # 更新止盈价格 (开仓价格 - 止损百分比)
                    position.take_profit_price = position.price * \
                        (1 - self.stop_loss_pct / 100)
                    # 调试信息，可以选择性记录
                    # self.write_log(f"空头更新最低价: {position.highest_price:.4f}, 止盈价: {position.take_profit_price:.4f}")

                # 检查是否触发止损
                stop_loss_price = position.price * \
                    (1 + self.stop_loss_pct / 100)
                if bar.close_price >= stop_loss_price:
                    # 触发止损
                    positions_to_close.append(
                        (position, "止损", bar.close_price))
                    self.write_log(
                        f"空头触发止损: 当前收盘价 {bar.close_price:.4f} >= 止损价 {stop_loss_price:.4f}")
                    continue

                # 检查是否触发移动止盈
                # 只有当价格曾经低于止盈线，且当前反弹到指定比例时才触发
                if position.highest_price < position.take_profit_price:
                    # 计算回撤价格 = 止盈价 - (止盈价 - 最低价) * (1 - 回撤比例)
                    retrace_price = position.take_profit_price - \
                        (position.take_profit_price -
                         position.highest_price) * (1 - self.profit_take_ratio)
                    # 使用收盘价与回撤价比较，而不是最高价
                    if bar.close_price >= retrace_price:
                        # 触发移动止盈
                        positions_to_close.append(
                            (position, "移动止盈", bar.close_price))
                        self.write_log(
                            f"空头触发移动止盈: 当前收盘价 {bar.close_price:.4f} >= 回撤价 {retrace_price:.4f} (最低价: {position.highest_price:.4f}, 止盈价: {position.take_profit_price:.4f})")
                        continue

        return positions_to_close

    def on_bar(self, bar: BarData):
        """
        K线更新回调
        """
        # 保存当前K线对象，供其他方法使用
        self.bar = bar

        # 获取信号值
        signal = getattr(bar, "signals", 0)

        # 检查现有持仓的止损止盈条件
        positions_to_close = self.check_stop_loss_take_profit(bar)

        # 执行平仓操作
        for position, reason, close_price in positions_to_close:
            # 计算盈亏
            if position.direction == "long":
                profit = (close_price - position.price) * \
                    position.volume * self.size
                # 平多仓
                self.sell(close_price, position.volume)
            else:  # short
                profit = (position.price - close_price) * \
                    position.volume * self.size
                # 平空仓
                self.cover(close_price, position.volume)

            # 计算手续费和滑点
            open_commission = position.price * position.volume * self.commission_rate
            close_commission = close_price * position.volume * self.commission_rate
            open_slippage = position.volume * self.size * self.slippage_rate
            close_slippage = position.volume * self.size * self.slippage_rate

            # 计算总手续费和总滑点
            total_commission = open_commission + close_commission
            total_slippage = open_slippage + close_slippage

            # 计算净盈亏（包含开仓和平仓的所有成本）
            net_profit = profit - total_commission - total_slippage

            # 计算盈亏百分比
            profit_pct = (profit / (position.price * position.volume)) * 100

            # 更新已使用资金
            if hasattr(self, '_used_capital'):
                self._used_capital -= position.price * position.volume

            # 记录平仓信息和盈亏
            if position.direction == "long":
                self.write_log(
                    f"{reason}平多：{bar.datetime}, 价格：{close_price}, 数量：{position.volume}, 开仓价：{position.price}, 盈亏：{profit:.2f}, 开仓手续费：{open_commission:.2f}, 平仓手续费：{close_commission:.2f}, 滑点：{total_slippage:.2f}, 净盈亏：{net_profit:.2f} ({profit_pct:.2f}%)")
            else:
                self.write_log(
                    f"{reason}平空：{bar.datetime}, 价格：{close_price}, 数量：{position.volume}, 开仓价：{position.price}, 盈亏：{profit:.2f}, 开仓手续费：{open_commission:.2f}, 平仓手续费：{close_commission:.2f}, 滑点：{total_slippage:.2f}, 净盈亏：{net_profit:.2f} ({profit_pct:.2f}%)")

            # 从持仓列表中移除
            self.positions.remove(position)

        # 检查是否有新的开仓信号
        if signal == 1:  # 做多信号
            self.open_long_position(bar)
        elif signal == -1:  # 做空信号
            self.open_short_position(bar)

    def open_long_position(self, bar: BarData):
        """
        开多仓

        参数:
        bar: 当前K线数据
        """
        # 计算开仓数量和所需资金
        if self.order_type == "quantity":
            # 按币数量下单
            volume = self.position_size
            required_capital = bar.close_price * volume
        elif self.order_type == "amount":
            # 按金额下单
            required_capital = self.position_size
            volume = required_capital / bar.close_price
        else:
            self.write_log(f"不支持的下单方式: {self.order_type}")
            return

        # 计算手续费和滑点
        commission = bar.close_price * volume * self.commission_rate
        slippage = volume * self.size * self.slippage_rate

        # 加上手续费和滑点
        total_cost = required_capital + commission + slippage

        # 获取可用资金
        available_capital = self.get_available_capital()

        # 检查资金是否充足
        if available_capital >= total_cost:
            # 资金充足，可以开仓
            self.buy(bar.close_price, volume)

            # 创建新的持仓对象并添加到持仓列表
            new_position = Position(
                "long", bar.close_price, volume, bar.datetime)

            # 设置止损价格和止盈价格
            new_position.stop_loss_price = bar.close_price * \
                (1 - self.stop_loss_pct / 100)
            new_position.take_profit_price = bar.close_price * \
                (1 + self.stop_loss_pct / 100)
            new_position.highest_price = bar.close_price  # 初始化最高价为开仓价

            self.positions.append(new_position)

            # 更新已使用资金
            if hasattr(self, '_used_capital'):
                self._used_capital += bar.close_price * volume

            # 强制重新计算可用资金
            self.get_available_capital(force_recalculate=True)

            # 记录开仓日志
            if self.order_type == "quantity":
                self.write_log(
                    f"做多(按数量)：{bar.datetime}, 价格：{bar.close_price}, 数量：{volume}, 手续费：{commission:.2f}, 滑点：{slippage:.2f}")
            else:
                self.write_log(
                    f"做多(按金额)：{bar.datetime}, 价格：{bar.close_price}, 金额：{self.position_size}, 数量：{volume}, 手续费：{commission:.2f}, 滑点：{slippage:.2f}")
        else:
            # 资金不足，记录到日志
            if self.order_type == "quantity":
                self.write_log(
                    f"资金不足，无法做多：{bar.datetime}, 价格：{bar.close_price}, 数量：{volume}, 所需资金：{total_cost:.2f}, 可用资金：{available_capital:.2f}")
            else:
                self.write_log(
                    f"资金不足，无法做多：{bar.datetime}, 价格：{bar.close_price}, 金额：{self.position_size}, 数量：{volume}, 所需资金：{total_cost:.2f}, 可用资金：{available_capital:.2f}")

    def open_short_position(self, bar: BarData):
        """
        开空仓

        参数:
        bar: 当前K线数据
        """
        # 计算开仓数量和所需资金
        if self.order_type == "quantity":
            # 按币数量下单
            volume = self.position_size
            required_capital = bar.close_price * volume
        elif self.order_type == "amount":
            # 按金额下单
            required_capital = self.position_size
            volume = required_capital / bar.close_price
        else:
            self.write_log(f"不支持的下单方式: {self.order_type}")
            return

        # 计算手续费和滑点
        commission = bar.close_price * volume * self.commission_rate
        slippage = volume * self.size * self.slippage_rate

        # 加上手续费和滑点
        total_cost = required_capital + commission + slippage

        # 获取可用资金
        available_capital = self.get_available_capital()

        # 检查资金是否充足
        if available_capital >= total_cost:
            # 资金充足，可以开仓
            self.short(bar.close_price, volume)

            # 创建新的持仓对象并添加到持仓列表
            new_position = Position(
                "short", bar.close_price, volume, bar.datetime)

            # 设置止损价格和止盈价格
            new_position.stop_loss_price = bar.close_price * \
                (1 + self.stop_loss_pct / 100)
            new_position.take_profit_price = bar.close_price * \
                (1 - self.stop_loss_pct / 100)
            new_position.highest_price = bar.close_price  # 初始化最低价为开仓价

            self.positions.append(new_position)

            # 更新已使用资金
            if hasattr(self, '_used_capital'):
                self._used_capital += bar.close_price * volume

            # 强制重新计算可用资金
            self.get_available_capital(force_recalculate=True)

            # 记录开仓日志
            if self.order_type == "quantity":
                self.write_log(
                    f"做空(按数量)：{bar.datetime}, 价格：{bar.close_price}, 数量：{volume}, 手续费：{commission:.2f}, 滑点：{slippage:.2f}")
            else:
                self.write_log(
                    f"做空(按金额)：{bar.datetime}, 价格：{bar.close_price}, 金额：{self.position_size}, 数量：{volume}, 手续费：{commission:.2f}, 滑点：{slippage:.2f}")
        else:
            # 资金不足，记录到日志
            if self.order_type == "quantity":
                self.write_log(
                    f"资金不足，无法做空：{bar.datetime}, 价格：{bar.close_price}, 数量：{volume}, 所需资金：{total_cost:.2f}, 可用资金：{available_capital:.2f}")
            else:
                self.write_log(
                    f"资金不足，无法做空：{bar.datetime}, 价格：{bar.close_price}, 金额：{self.position_size}, 数量：{volume}, 所需资金：{total_cost:.2f}, 可用资金：{available_capital:.2f}")
