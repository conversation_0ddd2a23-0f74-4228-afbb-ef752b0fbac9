import pandas as pd
from data_handler import <PERSON>Handler
from execution_handler import ExecutionHandler
from back_tester import BackTester
from statistics_handler import StatisticsHandler
from visualizer import Visualizer

# 示例执行
data = pd.DataFrame({'signals': [1, 0, -1, 1, 0]})  # 模拟数据
data_handler = DataHandler(data)
execution_handler = ExecutionHandler(
    close_logic=lambda signals, portfolio: 0,  # 示例平仓逻辑
    position_logic=lambda signals, portfolio: 100,  # 示例加仓逻辑
    config={'fee': 0.001, 'slippage': 0.01}  # 示例配置
)
backtester = BackTester(data_handler, execution_handler)
backtester.run_backtest()

# 生成可视化
visualization = Visualizer(backtester.results['equity_curve'])
visualization.plot_equity_curve()       # 账户净值变化
visualization.plot_drawdown()           # 回撤曲线
visualization.plot_daily_returns()      # 每日盈亏

performance_metrics = StatisticsHandler(
    backtester.results['equity_curve'], 1000000)
performance_metrics.calculate_metrics()
performance_metrics.print_metrics()
