class ExecutionHandler:
    """
    交易执行引擎
    
    功能：
    - 根据交易信号执行买卖操作
    - 计算交易成本（滑点+手续费）
    - 管理持仓状态和可用资金
    
    核心参数说明：
    :param position_management_fn: 仓位管理策略函数
        - 输入：当前价格、信号强度、持仓量、可用资金、建议交易量
        - 输出：实际执行交易量（经过风控调整）
    :param config: 交易配置字典
        - initial_capital: 初始资金
        - slippage: 滑点比例（默认0.2%）
        - fee_rate: 手续费率（默认0.03%）
    """
    def __init__(self, position_management_fn, config):
        self.position_mgr = position_management_fn
        self.config = config
        self.trade_log = []
        self.open_position_records = []
        self.position = {
            'shares': 0,
            'avg_cost': 0.0,
            'available_cash': config['initial_capital']
        }

    """
        执行交易并记录逐笔委托与成交信息。
        :param signals: 信号(1, 0, -1)
        :param price: 当前价格
        :param volume: 当前交易量
        :param date: 当前日期
        """

    def execute_trade(self, signals, price, volume, date):
        # 交易信号说明：
        # 交易信号不是买入或卖出就不处理
        # if signals != self.config['buy'] and signals != self.config['sell']:
        #     return

        # 仓位管理阶段：
        # 根据当前资金、持仓和风控规则，确定实际可交易量
        # 输出需要执行的交易信息数组
        trade_arr = self.position_mgr(
            signals=signals,
            price=price,
            position=self.position['shares'],
            position_avg_price=self.position['avg_cost'],
            open_position_records=self.open_position_records,
            cash=self.position['available_cash'],
            date=date,
            config=self.config
        )
        if len(trade_arr) > 0:
            # 执行每笔交易
            for trade in trade_arr:
                self.execute_trade_detail(signals, trade['signal'], trade['executed_price'], trade['trade_volume'], trade['fee'], trade['slippage'], date)


    def execute_trade_detail(self, signals, signal, executed_price, adjusted_volume, fee, slippage, date):
        # 确保数值精度
        adjusted_volume = float(adjusted_volume)
        executed_price = float(executed_price)
        fee = float(fee)

        if adjusted_volume != 0:
            # 不包含手续费和滑点的交易价值
            trade_value = abs(adjusted_volume * executed_price)
            # 手续费和滑点的交易价值
            slippage_fee =trade_value * (fee + slippage)
            if trade_value + slippage_fee > self.position['available_cash']:
                print(f"[WARN] 资金不足，无法执行交易！")
                return
            # 买入操作处理流程：
            if signal == self.config['buy']:
                # 保存原始持仓及持仓均价
                original_shares = self.position['shares']
                original_avg_cost = self.position['avg_cost']
                original_direction = 1 if original_shares >= 0 else -1

                # 更新持仓数量
                self.position['shares'] += adjusted_volume
                new_direction = 1 if self.position['shares'] >= 0 else -1

                # 计算新的平均持仓成本：
                if self.position['shares'] == 0:
                    # 如果新持仓为零，则为平仓，重置平均成本
                    self.position['avg_cost'] = 0
                    # 平多仓 可用资金 = 原可用资金 + 订单单价*订单数量 - 手续费 和 滑点费用
                    self.position['available_cash'] = self.position['available_cash'] + trade_value - slippage_fee
                elif original_shares == 0 or original_direction != new_direction:
                    # 方向发生转换（从空仓变多仓）
                    # 计算平仓获得的价值
                    get_val = -(executed_price - original_avg_cost) * abs(original_shares) + abs(
                        original_shares) * original_avg_cost
                    # 计算用于新方向开仓的交易部分
                    closing_volume = abs(original_shares)  # 用于平仓的数量
                    opening_volume = abs(adjusted_volume) - closing_volume  # 用于开新仓的数量

                    # 计算开仓价值
                    open_val = opening_volume * executed_price

                    # 计算新方向的均价（只考虑开新仓部分）
                    self.position['avg_cost'] = open_val / opening_volume
                    # 平多仓+开空仓 可用资金 = 原可用资金 + 平仓价值 - 开仓价值 - 手续费 和 滑点费用
                    self.position['available_cash'] = self.position['available_cash'] + get_val - open_val - slippage_fee
                    # 写入开仓记录
                    self.open_position_records.append({
                        'date': date,
                        'avg_price': executed_price,
                        'volume': opening_volume
                    })

                elif abs(self.position['shares']) > abs(original_shares) and original_direction == new_direction:

                    # 同方向加仓：(原成本 * 原持仓绝对值 + 新增成本) / 新持仓绝对值
                    self.position['avg_cost'] = (original_avg_cost * abs(original_shares) + trade_value) / abs(
                        self.position['shares'])
                    # 开多仓 可用资金 = 原可用资金 - 订单单价*订单数量 - 手续费 和 滑点费用
                    self.position['available_cash'] = self.position['available_cash'] - trade_value - slippage_fee

                    # 写入开仓记录
                    self.open_position_records.append({
                        'date': date,
                        'avg_price': executed_price,
                        'volume': adjusted_volume
                    })
                else:
                    get_val = -(executed_price - original_avg_cost) * adjusted_volume + original_avg_cost * adjusted_volume
                    # SHORT方向减仓，均价不变
                    self.position['available_cash'] = self.position['available_cash'] + get_val - slippage_fee

            # 卖出操作处理流程：
            elif signal == self.config['sell']:
                # 保存原始持仓 及 持仓价格
                original_shares = self.position['shares']
                original_avg_cost = self.position['avg_cost']
                original_direction = 1 if original_shares >= 0 else -1

                # 更新持仓数量
                self.position['shares'] -= adjusted_volume
                new_direction = 1 if self.position['shares'] >= 0 else -1

                # 处理持仓成本变化
                if self.position['shares'] == 0:
                    # 如果平仓至零，重置均价
                    self.position['avg_cost'] = 0
                    # 平空仓 可用资金 = 原可用资金 + 订单单价*订单数量 - 手续费 和 滑点费用
                    self.position['available_cash'] = self.position['available_cash'] + trade_value - slippage_fee
                elif original_direction != new_direction:
                    # 方向发生转换（从多仓变空仓）

                    # 计算平仓获得的价值
                    get_val = (executed_price - original_avg_cost) * original_shares + original_shares * original_avg_cost

                    # 计算用于新方向开仓的交易部分
                    opening_volume = abs(adjusted_volume) - original_shares  # 用于开新仓的数量

                    # 计算开空仓的价值
                    open_val = opening_volume * executed_price

                    # 计算新方向的均价（只考虑开新仓部分）
                    self.position['avg_cost'] = open_val / opening_volume
                    # 平空仓+开多仓 可用资金 = 原可用资金 + 平仓价值 - 开仓价值 - 手续费 和 滑点费用
                    self.position['available_cash'] = self.position['available_cash'] + get_val - open_val - slippage_fee

                    # 写入开仓记录
                    self.open_position_records.append({
                        'date': date,
                        'avg_price': executed_price,
                        'volume': -opening_volume
                    })
                elif original_direction == -1 and abs(self.position['shares']) > abs(original_shares):
                    # 同方向加仓（卖出增加空头）：(原成本 * 原持仓绝对值 + 新增成本) / 新持仓绝对值
                    self.position['avg_cost'] = (original_avg_cost * abs(original_shares) + trade_value) / abs(
                        self.position['shares'])
                    # 开空仓 可用资金 = 原可用资金 - 订单单价*订单数量 - 手续费
                    self.position['available_cash'] = self.position['available_cash'] - trade_value - slippage_fee
                    # 写入开仓记录
                    self.open_position_records.append({
                        'date': date,
                        'avg_price': executed_price,
                        'volume': -adjusted_volume
                    })
                else:
                    # LONG方向减仓，均价不变
                    get_val = (executed_price - original_avg_cost) * adjusted_volume + original_avg_cost * adjusted_volume
                    self.position['available_cash'] = self.position['available_cash'] + get_val - trade_value * (fee + slippage)

            # 净值
            value = round(self.position['available_cash'] + self.position['avg_cost'] * abs(self.position['shares']), 2)

            # 记录交易日志
            self.trade_log.append({
                'date': date,
                'signal': signals,
                'fn_signal': signal,
                'price': executed_price,
                'volume': adjusted_volume,
                'slippage_fee': round(slippage_fee, 2),
                'position': self.position['shares'],
                'position_avg_price': round(self.position['avg_cost'], 2),
                'cash': round(self.position['available_cash'], 2),
                'value': value
            })
    """
    执行交易并记录逐笔委托与成交信息。
    :param signals: 信号(1, 0, -1)
    :param price: 当前价格
    :param volume: 当前交易量
    :param date: 当前日期
    """

    def execute_trade_old(self, signals, price, volume, date):
        # 交易信号说明：
        # 交易信号不是买入或卖出就不处理
        # if signals != self.config['buy'] and signals != self.config['sell']:
        #     return

        # 仓位管理阶段：
        # 根据当前资金、持仓和风控规则，确定实际可交易量
        adjusted_volume, executed_price, fee, signal = self.position_mgr(
            signals=signals,
            price=price,
            position=self.position['shares'],
            position_avg_price=self.position['avg_cost'],
            cash=self.position['available_cash'],
            date=date,
            config=self.config
        )

        # 确保数值精度
        adjusted_volume = float(adjusted_volume)
        executed_price = float(executed_price)
        fee = float(fee)

        if adjusted_volume != 0:
            if abs(adjusted_volume * executed_price) + fee > self.position['available_cash']:
                print(f"[WARN] 资金不足，无法执行交易！")
                return
            # 买入操作处理流程：
            if signal == self.config['buy']:
                # 保存原始持仓及持仓均价
                original_shares = self.position['shares']
                original_avg_cost = self.position['avg_cost']
                original_direction = 1 if original_shares >= 0 else -1

                # 更新持仓数量
                self.position['shares'] += adjusted_volume
                new_direction = 1 if self.position['shares'] >= 0 else -1

                # 计算交易成本（考虑方向）
                trade_value = executed_price * adjusted_volume

                # 计算新的平均持仓成本：
                if self.position['shares'] == 0:
                    # 如果新持仓为零，则为平仓，重置平均成本
                    self.position['avg_cost'] = 0
                    # 平多仓 可用资金 = 原可用资金 + 订单单价*订单数量 - 手续费
                    self.position['available_cash'] = self.position['available_cash'] + abs(trade_value) - abs(fee)
                elif original_shares == 0 or original_direction != new_direction:
                    # 方向发生转换（从空仓变多仓）
                    # 计算平仓获得的价值
                    get_val = -(executed_price - original_avg_cost) * abs(original_shares) + abs(original_shares) * original_avg_cost
                    # 计算用于新方向开仓的交易部分
                    closing_volume = abs(original_shares)  # 用于平仓的数量
                    opening_volume = abs(adjusted_volume) - closing_volume  # 用于开新仓的数量

                    # 计算开仓价值
                    open_val = opening_volume * executed_price

                    # 计算新方向的均价（只考虑开新仓部分）
                    self.position['avg_cost'] = open_val / opening_volume
                    # 平多仓+开空仓 可用资金 = 原可用资金 + 平仓价值 - 开仓价值 - 手续费
                    self.position['available_cash'] = self.position['available_cash'] + get_val - open_val - fee

                elif abs(self.position['shares']) > abs(original_shares) and original_direction == new_direction:
                    # 同方向加仓：(原成本 * 原持仓绝对值 + 新增成本) / 新持仓绝对值
                    self.position['avg_cost'] = (self.position['avg_cost'] * abs(original_shares) + trade_value) / abs(self.position['shares'])
                    # 开多仓 可用资金 = 原可用资金 - 订单单价*订单数量 - 手续费
                    self.position['available_cash'] = self.position['available_cash'] - abs(trade_value) - abs(fee)
                # 同方向减仓，均价不变

            # 卖出操作处理流程：
            elif signal == self.config['sell']:
                # 保存原始持仓 及 持仓价格
                original_shares = self.position['shares']
                original_avg_cost = self.position['avg_cost']
                original_direction = 1 if original_shares >= 0 else -1

                # 更新持仓数量
                self.position['shares'] -= adjusted_volume
                new_direction = 1 if self.position['shares'] >= 0 else -1

                # 计算交易价值和到账金额
                trade_value = executed_price * adjusted_volume

                # 处理持仓成本变化
                if self.position['shares'] == 0:
                    # 如果平仓至零，重置均价
                    self.position['avg_cost'] = 0
                    # 平空仓 可用资金 = 原可用资金 + 订单单价*订单数量 - 手续费
                    self.position['available_cash'] = self.position['available_cash'] + abs(trade_value) - abs(fee)
                elif original_direction != new_direction:
                    # 方向发生转换（从多仓变空仓）

                    # 计算平仓获得的价值
                    get_val = (executed_price - original_avg_cost) * original_shares + original_shares * original_avg_cost

                    # 计算用于新方向开仓的交易部分
                    opening_volume = abs(adjusted_volume) - original_shares  # 用于开新仓的数量

                    # 计算开空仓的价值
                    open_val = opening_volume * executed_price

                    # 计算新方向的均价（只考虑开新仓部分）
                    self.position['avg_cost'] = open_val / opening_volume
                    # 平空仓+开多仓 可用资金 = 原可用资金 + 平仓价值 - 开仓价值 - 手续费
                    self.position['available_cash'] = self.position['available_cash'] + get_val - open_val - fee
                elif original_direction == -1 and abs(self.position['shares']) > abs(original_shares):
                    # 同方向加仓（卖出增加空头）：(原成本 * 原持仓绝对值 + 新增成本) / 新持仓绝对值
                    self.position['avg_cost'] = (self.position['avg_cost'] * abs(original_shares) + trade_value) / abs(self.position['shares'])
                    # 开空仓 可用资金 = 原可用资金 - 订单单价*订单数量 - 手续费
                    self.position['available_cash'] = self.position['available_cash'] - abs(trade_value) - abs(fee)
                # 其他情况（同方向减仓）均价不变

            # 净值
            value = round(self.position['available_cash'] + self.position['avg_cost'] * abs(self.position['shares']), 2)

            # 记录交易日志
            self.trade_log.append({
                'date': date,
                'signal': signals,
                'fn_signal': signal,
                'price': executed_price,
                'volume': adjusted_volume,
                'fee': round(fee, 2),
                'position': round(self.position['shares'], 2),
                'position_avg_price': round(self.position['avg_cost'], 2),
                'cash': round(self.position['available_cash'], 2),
                'value': value
            })
        else:
            print(f"[WARN] 交易信号{signals}无效，可交易量为零，不执行交易！")

    """
    获取逐笔交易记录
    :return: 逐笔交易记录
    """
    def get_trade_log(self):
        return self.trade_log

    """
    打印逐笔交易记录
    """
    def print_trade_log(self):
        for log in self.trade_log:
            print(f"{log['date']} | {'买入' if log['signal'] == 1 else '卖出'} "
                  f"{log['volume']}股 @ {log['price']:.2f}  "
                  f"手续费:{log['fee']:.2f} 持仓:{log['position']} 现金:{log['cash']:.2f}")