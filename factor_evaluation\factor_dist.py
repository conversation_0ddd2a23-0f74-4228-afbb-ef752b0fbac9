import pandas as pd


def analyze_factor_distribution(df, factor_col='signals', time_windows=10):
    """
    因子分布分析工具

    参数:
    df : 包含DatetimeIndex的DataFrame
    factor_col : 要分析的因子列名
    time_windows : int或list,分割的时间窗口数量(默认10)
    """
    # ======== 参数处理 ==========
    if isinstance(time_windows, int):
        time_windows = [time_windows]

    # ======== 基础分布特征 ==========
    desc_stats = df[factor_col].describe(
        percentiles=[.01, .05, .25, .5, .75, .95, .99])

    # ======== 极端值分析 ==========
    outlier_analysis = pd.Series({
        '>3std': (df[factor_col].abs() > 3*desc_stats['std']).mean(),
        '>5std': (df[factor_col].abs() > 5*desc_stats['std']).mean()
    })

    # ======== 时间窗口分布 ==========
    window_stats = {}

    # 获取时间范围
    min_time = df.index.min()
    max_time = df.index.max()

    for n in time_windows:
        # 生成等分时间点(增加容错处理)
        try:
            bins = pd.date_range(start=min_time, end=max_time, periods=n+1)
        except Exception as e:
            raise ValueError(f"无法分割为{n}个窗口: {str(e)}")

        # 分割时间窗口并分组统计(添加observed参数)
        intervals = pd.cut(df.index, bins=bins, right=False)

        # 使用lambda函数计算峰度(修正方法名)
        grouped = df.groupby(intervals, observed=False)[factor_col].agg(
            ['mean', 'std', 'skew', lambda x: x.kurt(), 'count']
        ).rename(columns={'<lambda_0>': 'kurtosis'})

        # 计算每个窗口的时间范围
        time_ranges = [f"{iv.left.strftime('%Y-%m-%d')} ~ {iv.right.strftime('%Y-%m-%d')}"
                       for iv in grouped.index.categories]

        # 存储结果
        window_stats[n] = {
            'stats': grouped,
            'time_ranges': time_ranges
        }

    return {
        'descriptive_stats': desc_stats,
        'outlier_analysis': outlier_analysis,
        'window_stats': window_stats
    }
