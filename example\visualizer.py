import pickle
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
import numpy as np
import plotly.io as pio


class Visualizer:
    def __init__(self):
        pass

    def plot_equity_curve(self, equity_curve):
        """
        绘制账户净值曲线
        :param equity_curve: 账户净值曲线
        """
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=equity_curve.index, y=equity_curve,  # x 和 y 数据
            mode='lines',  # 设置为线条模式
            name='sin(x)',  # 曲线名称
            line=dict(color='blue', width=2),  # 设置线条颜色和宽度
            hoverinfo='x+y'  # 悬停时显示 x 和 y 值
        ))
        fig.update_layout(
            title='账户净值曲线',
            xaxis_title='Time',
            yaxis_title='Balance',
            dragmode='zoom',
            xaxis=dict(rangeslider=dict(visible=True)),
            yaxis=dict(autorange=True),  # 添加这一行
            hovermode='x unified')

        # 显示图表
        # fig.show()
        with open(r'C:\Users\<USER>\Documents\WXWork\1688857080721880\Cache\File\2025-04\20250411.pkl', 'wb')as f:
            pickle.dump(equity_curve, f)

    def plot_drawdown(self, equity_curve):
        """
        绘制账户回撤
        :param equity_curve: 账户净值曲线
        """
        peak = equity_curve.cummax()
        # 计算回撤
        drawdown = (equity_curve - peak) / peak

        # 创建子图
        fig = make_subplots(rows=2, cols=1,
                            shared_xaxes=True,
                            vertical_spacing=0.03,
                            row_heights=[0.7, 0.3])
        # 添加净值线到第一个子图
        fig.add_trace(
            go.Scatter(
                x=equity_curve.index,
                y=equity_curve,
                mode='lines',
                name='账户净值',
                line=dict(color='rgb(0, 100, 180)', width=2)
            ),
            row=1, col=1
        )

        # 添加回撤到第二个子图
        fig.add_trace(
            go.Bar(
                x=equity_curve.index,
                y=drawdown,
                name='回撤',
                marker_color='rgba(255,0,0,0.5)'
            ),
            row=2, col=1
        )

        # 更新布局
        fig.update_layout(
            title='账户净值与回撤分析',
            height=800,
            legend=dict(orientation="h", yanchor="bottom",
                        y=1.02, xanchor="right", x=1),
            margin=dict(l=60, r=60, t=50, b=50)
        )

        # 更新Y轴标题
        fig.update_yaxes(title_text='净值', row=1, col=1)
        fig.update_yaxes(title_text='回撤', tickformat='.2%', row=2, col=1)

        # 更新X轴
        fig.update_xaxes(title_text='日期', row=2, col=1)

        # 显示图表
        fig.show()

    def plot_daily_returns(self, daily_returns):
        """
        绘制每日盈亏
        :param daily_returns: 每日收益
        """
        fig = go.Figure()

        # 添加柱状图
        fig.add_trace(go.Bar(
            x=daily_returns.index, y=daily_returns,  # x 为日期，y 为每日盈亏
            name='每日盈亏',
            marker_color=np.where(daily_returns >= 0,
                                  'green', 'red')  # 盈利为绿色，亏损为红色
        ))
        # 设置图表布局
        fig.update_layout(
            title='每日盈亏柱状图',
            xaxis_title='日期',
            yaxis_title='盈亏金额',
            template='plotly_white',
            hovermode='x unified'
        )
        # 显示图表
        # fig.show()

    def plot_return_distribution(self, daily_returns):
        """
        绘制收益分布情况
        :param daily_returns: 每日收益
        """

        fig = px.histogram(
            daily_returns,
            histnorm='percent',
            title='收益分布',
            labels={'total_bill': '收益'},  # 自定义轴标签
            nbins=100,
            opacity=0.8,  # 设置透明度
        )
        fig.update_traces(marker_line=dict(width=1.5, color='black'))
        fig.update_layout(title={'text': '收益分布情况'})
        # fig.show()
