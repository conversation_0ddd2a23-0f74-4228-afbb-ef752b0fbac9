from factors_lib.chao_factors_lib import Chao<PERSON><PERSON><PERSON>ib
from factors_lib.filled_factors import FilledFactorAll
from factors_lib.base_factor import BaseFactor
from factors_lib.filled_factor_800 import FilledFactor800
from factors_lib.version1 import VersionOne
from factors_lib.final_factors import FinalFactor
from factors_lib.server_factors import FactorD<PERSON>patcher
from factors_lib.hide_factors import HideFactors
import csv

import inspect


methods = [name for name, func in inspect.getmembers(
    HideFactors, predicate=inspect.isfunction) if name != "__init__"]


print("方法数量:", len(methods))
# for item in methods:
#     if not item[-1].isdigit():  # 判断字符串最后一个字符是否是数字
#         print("False")


# 将数据写入 CSV 文件
with open('workspace/hide_name.csv', mode='w', newline='') as file:
    writer = csv.writer(file)

    # 写入表头
    writer.writerow(['factor_name'])

    # 写入数据
    for item in methods:
        writer.writerow([item])  # 每一项写入一行
