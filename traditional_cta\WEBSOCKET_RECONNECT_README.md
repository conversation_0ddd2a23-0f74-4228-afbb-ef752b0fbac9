# WebSocket重连功能说明

## 概述

为了解决WebSocket连接断开的风险，我们为 `websocket_binance.py` 添加了完整的重连机制。该机制包括自动重连、心跳检测、指数退避策略等功能，确保交易系统的稳定性和可靠性。

## 主要功能

### 1. 自动重连机制
- **智能重连**: 连接断开时自动尝试重连
- **指数退避**: 重连间隔逐渐增加，避免频繁重连
- **最大重试**: 可配置最大重试次数，防止无限重连

### 2. 心跳检测
- **定期心跳**: 定时发送ping消息检测连接状态
- **超时检测**: 检测pong响应超时，及时发现连接问题
- **可配置**: 可以启用/禁用心跳检测功能

### 3. 连接状态管理
- **状态跟踪**: 实时跟踪连接状态
- **状态保存**: 连接断开时自动保存交易状态
- **状态恢复**: 重连成功后自动恢复订阅

## 配置参数

在 `websocket_binance.py` 文件中的 `RECONNECT_CONFIG` 部分：

```python
RECONNECT_CONFIG = {
    "max_retries": 10,                  # 最大重试次数，-1表示无限重试
    "initial_delay": 1,                 # 初始重连延迟（秒）
    "max_delay": 60,                    # 最大重连延迟（秒）
    "backoff_factor": 2,                # 退避因子
    "ping_interval": 30,                # 心跳间隔（秒）
    "ping_timeout": 10,                 # 心跳超时（秒）
    "enable_ping": True                 # 是否启用心跳检测
}
```

### 参数说明

- **max_retries**: 最大重试次数
  - 设置为 -1 表示无限重试
  - 建议设置为 10-20 次
  
- **initial_delay**: 初始重连延迟
  - 第一次重连的等待时间
  - 建议设置为 1-5 秒
  
- **max_delay**: 最大重连延迟
  - 重连延迟的上限
  - 建议设置为 60-300 秒
  
- **backoff_factor**: 退避因子
  - 每次重连延迟的倍数
  - 建议设置为 1.5-2.0
  
- **ping_interval**: 心跳间隔
  - 发送心跳的时间间隔
  - 建议设置为 20-60 秒
  
- **ping_timeout**: 心跳超时
  - 等待心跳响应的超时时间
  - 建议设置为 5-15 秒
  
- **enable_ping**: 是否启用心跳检测
  - True: 启用心跳检测
  - False: 禁用心跳检测

## 重连策略

### 指数退避算法

重连延迟计算公式：
```
delay = min(initial_delay * (backoff_factor ^ (retry_count - 1)), max_delay)
```

示例（initial_delay=1, backoff_factor=2, max_delay=60）：
- 第1次重连: 1秒后
- 第2次重连: 2秒后
- 第3次重连: 4秒后
- 第4次重连: 8秒后
- 第5次重连: 16秒后
- 第6次重连: 32秒后
- 第7次重连: 60秒后（达到上限）

## 使用方法

### 1. 正常启动
```bash
python websocket_binance.py
```

系统会自动使用配置的重连参数。

### 2. 查看重连日志
重连相关的日志会记录在 `logs/trading_system_YYYYMMDD.log` 文件中，包括：
- 连接建立/断开事件
- 重连尝试记录
- 心跳检测状态
- 错误信息

### 3. 监控连接状态
日志中会显示以下信息：
- 🔗 WebSocket连接已建立
- 🔌 WebSocket连接已关闭
- 🔄 将在 X 秒后尝试第 N 次重连
- 📡 已订阅数据流
- ⚠️ 心跳超时，可能连接已断开

## 测试功能

提供了测试脚本 `test_reconnect.py` 用于验证重连功能：

```bash
python test_reconnect.py
```

测试内容包括：
- 配置参数验证
- 重连逻辑测试
- 连接状态模拟

## 故障排除

### 常见问题

1. **重连失败**
   - 检查网络连接
   - 检查代理设置
   - 查看错误日志

2. **心跳超时**
   - 调整 ping_timeout 参数
   - 检查网络延迟
   - 考虑禁用心跳检测

3. **频繁重连**
   - 增加 initial_delay
   - 调整 backoff_factor
   - 检查网络稳定性

### 日志分析

重要的日志关键词：
- `WebSocket错误`: 连接错误
- `重连`: 重连尝试
- `心跳`: 心跳检测
- `订阅`: 数据订阅状态

## 注意事项

1. **资源使用**: 心跳检测会增加少量网络流量
2. **延迟影响**: 重连过程中可能错过部分数据
3. **状态保存**: 系统会在断开时自动保存交易状态
4. **手动干预**: 可以通过 Ctrl+C 安全停止系统

## 更新日志

- **v1.0**: 添加基础重连功能
- **v1.1**: 增加心跳检测
- **v1.2**: 优化重连策略和日志记录
