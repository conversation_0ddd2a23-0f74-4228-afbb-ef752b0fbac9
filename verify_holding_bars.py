#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证holding_bars是否被错误理解为天数
"""

import sys
sys.path.append('.')

import pandas as pd
from vnpy_backtester.scripts.run_multi_position_strategy import run_multi_position_strategy_backtest

def verify_holding_bars_calculation():
    """验证holding_bars计算"""
    print('=== 验证holding_bars是否被错误理解为天数 ===')
    
    # 读取数据
    df = pd.read_pickle(r'C:\Users\<USER>\Desktop\kline_data_2025-05-30_14儶57儶15.pkl')
    df['signals'] = df['signal']
    df.index = pd.to_datetime(df.index)
    
    # 使用07-11到07-13期间的数据
    df_test = df['2023-07-11':'2023-07-13']
    print(f'数据长度: {len(df_test)}条K线')
    print(f'数据频率: 15分钟')
    print(f'每日K线数: {24*60/15} = 96条')
    print(f'3天总K线数: {3*96} = 288条')
    
    # 运行回测
    engine = run_multi_position_strategy_backtest(
        df=df_test,
        holding_bars=10,
        position_size=1000,
        rate=0.0003,
        slippage=0.000,
        capital=15000,
        order_type='amount',
        max_positions=10,
        plot_show=False,
        plot_save=False
    )
    
    print('\n=== 分析交易记录 ===')
    
    # 分析交易记录，验证持仓时间
    trades = list(engine.trades.values())
    
    # 按时间排序
    trades.sort(key=lambda x: x.datetime)
    
    # 分析开仓和平仓的配对
    open_trades = {}  # 存储开仓交易
    closed_pairs = []  # 存储配对的开平仓
    
    for trade in trades:
        if trade.offset.value == "开":
            # 开仓交易
            key = f"{trade.direction.value}_{trade.price}_{trade.volume}"
            open_trades[key] = trade
        else:
            # 平仓交易，寻找对应的开仓
            key = f"{'多' if trade.direction.value == '空' else '空'}_{trade.price}_{trade.volume}"
            
            # 寻找匹配的开仓交易（价格和数量相近）
            matched_open = None
            for open_key, open_trade in open_trades.items():
                if (abs(open_trade.volume - trade.volume) < 0.1 and
                    ((trade.direction.value == "多" and open_trade.direction.value == "空") or
                     (trade.direction.value == "空" and open_trade.direction.value == "多"))):
                    matched_open = open_trade
                    break
            
            if matched_open:
                # 计算持仓时间
                time_diff = trade.datetime - matched_open.datetime
                bars_held = time_diff.total_seconds() / (15 * 60)  # 15分钟一根K线
                
                closed_pairs.append({
                    'open_time': matched_open.datetime,
                    'close_time': trade.datetime,
                    'direction': matched_open.direction.value,
                    'bars_held': bars_held,
                    'time_diff': time_diff
                })
                
                # 从开仓字典中移除
                del open_trades[open_key]
    
    print(f'找到 {len(closed_pairs)} 对完整的开平仓交易')
    
    # 分析持仓时间分布
    bars_held_list = [pair['bars_held'] for pair in closed_pairs]
    
    if bars_held_list:
        print(f'\n持仓K线数统计:')
        print(f'最小持仓K线数: {min(bars_held_list):.1f}')
        print(f'最大持仓K线数: {max(bars_held_list):.1f}')
        print(f'平均持仓K线数: {sum(bars_held_list)/len(bars_held_list):.1f}')
        
        # 统计持仓10根K线的交易数量
        ten_bar_trades = [b for b in bars_held_list if abs(b - 10) < 0.5]
        print(f'持仓10根K线的交易数量: {len(ten_bar_trades)}')
        print(f'占比: {len(ten_bar_trades)/len(bars_held_list)*100:.1f}%')
        
        # 显示前10个交易的详细信息
        print(f'\n前10个交易的持仓时间:')
        for i, pair in enumerate(closed_pairs[:10]):
            print(f'{i+1}. {pair["open_time"]} -> {pair["close_time"]} '
                  f'({pair["direction"]}) 持仓{pair["bars_held"]:.1f}根K线')
    
    print('\n=== 检查daily_results的持仓计算 ===')
    
    # 检查daily_results中的持仓计算
    daily_results = engine.daily_results
    dates = sorted(daily_results.keys())
    
    for date in dates:
        result = daily_results[date]
        print(f'{date}:')
        print(f'  开始持仓: {result.start_pos:.2f}')
        print(f'  结束持仓: {result.end_pos:.2f}')
        print(f'  持仓变化: {result.end_pos - result.start_pos:.2f}')
        print(f'  交易数量: {result.trade_count}')
        
        if hasattr(result, 'trade_list') and result.trade_list:
            open_count = sum(1 for t in result.trade_list if t.offset.value == "开")
            close_count = sum(1 for t in result.trade_list if t.offset.value == "平")
            print(f'  开仓交易: {open_count}, 平仓交易: {close_count}')
        print()
    
    print('=== 结论 ===')
    if bars_held_list and len(ten_bar_trades) > len(bars_held_list) * 0.8:
        print('✅ holding_bars=10被正确理解为10根K线，不是10天')
    else:
        print('❌ holding_bars可能被错误理解，需要进一步检查')
    
    return engine

if __name__ == '__main__':
    verify_holding_bars_calculation()
