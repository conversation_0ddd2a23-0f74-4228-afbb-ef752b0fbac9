# This is a sample Python script.
import back_tester
import back_tester_two_way
import data_handler
import execution_handler_two_way
import statistics_handler
import visualizer
import execution_handler
import pandas as pd
import requests
from sqlalchemy import create_engine
from factor_evaluation.data_service import DataService
from functools import partial


def position_management_fn1_two_way(signals, price, position, open_position_records, date, config):
    trade_volume = 0.1
    executed_price = price
    fee = 0.0005

    slippage = config.get('slippage', 0.002)
    fee_rate = config.get('fee_rate', 0.0003)

    time_threshold_minutes = config.get('time_threshold_minutes', 96 * 15)

    # 计算实际时间阈值（转换为毫秒）
    time_threshold = time_threshold_minutes * 60 * 1000  # 分钟转毫秒

    # 当前时间大于开仓时间 5根k以上(15m级)

    def condition(x): return date - x['date'] >= time_threshold
    # 已到平仓时间的开仓数据
    need_close_records = [x for x in open_position_records if condition(x)]
    # 从原列表中删除（已处理的开仓记录数据） 这里是需要直接修改原数据
    open_position_records[:] = [
        x for x in open_position_records if not condition(x)]  # 原地修改
    trade_arr = []
    # 可以优化成平多 和 平空 两个交易单，就不用每个append
    for record in need_close_records:
        # todo: 写入平仓交易单trade_arr.append()
        signal_t = config['sell']
        if record['side'] == 'LONG':
            signal_t = config['sell']
        elif record['side'] == 'SHORT':
            signal_t = config['buy']
        else:
            continue
        # 写入平仓单
        trade_arr.append({
            # 交易信号（数值同配置的买卖信号数值）
            'signal': signal_t,
            # 交易方向 SHORT LONG
            'side': record['side'],
            # 交易数量
            'trade_volume': record['volume'],
            # 交易价格
            'executed_price': executed_price,
            # 手续费
            'fee': fee,
            # 滑点
            'slippage': slippage
        })
    side = 'SHORT'
    if signals == config['sell']:
        side = 'SHORT'
        trade_vol = trade_volume
    elif signals == config['buy']:
        side = 'LONG'
        trade_vol = trade_volume
    else:
        trade_vol = 0
    # 因子信号只有开仓
    trade_arr.append({
        # 交易信号（数值同配置的买卖信号数值）
        'signal': signals,
        # 交易方向 SHORT LONG
        'side': side,
        # 交易数量
        'trade_volume': trade_vol,
        # 交易价格
        'executed_price': executed_price,
        # 手续费
        'fee': fee,
        # 滑点
        'slippage': slippage
    })
    # 可多次交易
    return trade_arr


def position_management_fn1(signals, price, position, position_avg_price, open_position_records, cash, date, config):
    """
    v0.1版本(出现反向信号,全平反手)
    """
    trade_volume = 0.1

    executed_price = price
    fee = 0.0005
    slippage = config.get('slippage', 0.002)
    fee_rate = config.get('fee_rate', 0.0003)

    trade_arr = []
    #  (当前价格 - 持仓均价) * 仓位数量
    profit = (price - position_avg_price) * position
    # 当前持仓达到止盈或止损
    if profit >= 5 or profit <= -5:
        signal = 0
        trade_num = abs(position)
        if position > 0:
            signal = config['sell']
        elif position < 0:
            signal = config['buy']
        else:
            trade_num = 0
        # 开一个平仓单
        trade_arr.append({
            # 交易信号
            'signal': signal,
            'trade_volume': trade_num,
            'executed_price': executed_price,
            'fee': fee,
            'slippage': slippage
        })

    # 开因子信号的单
    trade_arr.append({
        # 因子的信号
        'signal': signals,
        'trade_volume': trade_volume,
        'executed_price': executed_price,
        'fee': fee,
        'slippage': slippage
    })

    return trade_arr


def position_management_fn2(signals, price, position, position_avg_price, open_position_records, cash, date, config):
    """
    v0.2版本(同向信号不延续就全平)
    """
    global settings
    trade_volume = 0
    if position > 0 and signals == 0:
        trade_volume = position
    elif position < 0 and signals == 0:
        trade_volume = -position
    elif signals != 0:
        trade_volume = 0.1

    executed_price = price
    fee = 0.0005

    slippage = config.get('slippage', 0.002)
    fee_rate = config.get('fee_rate', 0.0003)

    # 最多涉及两次交易
    trade_info_one = {
        # 交易信号
        'signal': signals,
        'trade_volume': trade_volume,
        'executed_price': executed_price,
        'fee': fee,
        'slippage': slippage
    }
    return [trade_info_one]

    """
    修复后的单向持仓回测函数
    """
    # 合并配置
    merged_config = {
        'data_handler': {**settings["data_handler"], **(config.get('data_handler', {}) if config else {})},
        'execution_handler': {**settings["execution_handler"], **(config.get('execution_handler', {}) if config else {})},
        'back_tester': {**settings["back_tester"], **(config.get('back_tester', {}) if config else {})}
    }

    # 修正仓位管理函数选择逻辑
    if position_management_fn:
        # 确保配置注入
        if not hasattr(position_management_fn, 'keywords') or 'config' not in position_management_fn.keywords:
            pm_fn = partial(position_management_fn,
                            config=merged_config['execution_handler'])
        else:
            pm_fn = position_management_fn
    else:
        pm_fn = partial(
            position_management_fn1,
            config=merged_config['execution_handler']
        )

    # 数据处理类
    datahandler = data_handler.DataHandler(
        data,
        config=merged_config["data_handler"]
    )

    # 交易执行引擎
    executionhandler = execution_handler.ExecutionHandler(
        position_management_fn=pm_fn,
        config=merged_config["execution_handler"]
    )

    # 生成可视化类
    visualizerh = visualizer.Visualizer()
    # 计算统计类
    performance_metrics = statistics_handler.StatisticsHandler()

    # 回测类
    backtester = back_tester.BackTester(
        datahandler=datahandler,
        executionhandler=executionhandler,
        statisticshandler=performance_metrics,
        visualizer=visualizerh,
        config=merged_config["back_tester"]
    )

    backtester.run_backtest()
    return performance_metrics, visualizerh


def position_management_fn3(signals, price, position, position_avg_price, open_position_records, cash, date, config):
    """
    v0.1版本(出现反向信号,全平反手)
    """
    global settings
    if position > 0 and signals < 0:
        trade_volume = position+0.1
    elif position < 0 and signals > 0:
        trade_volume = -position+0.1
    else:
        trade_volume = 0.1

    executed_price = price
    fee = 0.0005
    slippage = config.get('slippage', 0.002)
    fee_rate = config.get('fee_rate', 0.0003)

    trade_arr = []
    #  (当前价格 - 持仓均价) * 仓位数量
    # profit = (price - position_avg_price) * position
    # # 当前持仓达到止盈或止损
    # if profit >= 5 or profit <= -5:
    #     signal = 0
    #     trade_num = abs(position)
    #     if position > 0:
    #         signal = config['sell']
    #     elif position < 0:
    #         signal = config['buy']
    #     else:
    #         trade_num = 0
    #     # 开一个平仓单
    #     trade_arr.append({
    #         # 交易信号
    #         'signal': signal,
    #         'trade_volume': trade_num,
    #         'executed_price': executed_price,
    #         'fee': fee,
    #         'slippage': slippage
    #     })

    # 开因子信号的单
    trade_arr.append({
        # 因子的信号
        'signal': signals,
        'trade_volume': trade_volume,
        'executed_price': executed_price,
        'fee': fee,
        'slippage': slippage
    })

    return trade_arr


# 双向持仓
def fun_backtester_two_way(data, position_management_fn):
    # 数据处理类
    datahandler = data_handler.DataHandler(
        data, config=settings["data_handler"])

    if not position_management_fn:
        fun = position_management_fn
    else:
        fun = position_management_fn1_two_way

    # 交易执行引擎
    executionhandler = execution_handler_two_way.ExecutionHandlerTwoWay(
        position_management_fn=fun,
        config=settings["execution_handler"]
    )
    # 生成可视化类
    visualizerh = visualizer.Visualizer()
    # 计算统计类
    performance_metrics = statistics_handler.StatisticsHandler()

    # 回测回测类
    backtester = back_tester_two_way.BackTesterTwoWay(datahandler,
                                                      executionhandler,
                                                      performance_metrics,
                                                      visualizerh,
                                                      config=settings["back_tester"])

    backtester.run_backtest()

    global results
    results = backtester.performance_metrics.results


# 单向持仓
def fun_backtester(data, position_management_fn):

    if not position_management_fn:
        fun = position_management_fn3
    else:
        fun = position_management_fn

    # 数据处理类
    datahandler = data_handler.DataHandler(
        data, config=settings["data_handler"])

    # 交易执行引擎
    executionhandler = execution_handler.ExecutionHandler(
        position_management_fn=fun,
        config=settings["execution_handler"]
    )
    # 生成可视化类
    visualizerh = visualizer.Visualizer()
    # 计算统计类
    performance_metrics = statistics_handler.StatisticsHandler()

    # 回测回测类
    backtester = back_tester.BackTester(datahandler,
                                        executionhandler,
                                        performance_metrics,
                                        visualizerh,
                                        config=settings["back_tester"])

    backtester.run_backtest()
    global results
    results = backtester.performance_metrics.results


def execute(data, engine_type, position_management_fn=None):

    # 设置默认的仓位管理函数
    if position_management_fn is None:
        if engine_type == 'two_way':
            position_management_fn = position_management_fn1_two_way
        else:
            position_management_fn = position_management_fn3

    # 根据模式调用对应的回测函数
    if engine_type == 'two_way':
        fun_backtester_two_way(data, position_management_fn)
    else:
        fun_backtester(data, position_management_fn)


settings = {
    # 你要回测的数据
    "table": "ETHUSDT_15m_2022_2025",
    "method": "ret_hv_ratio_signals",
    # 切片，from
    "from_data": -9999999,
    # 切片，to，如果填0就切到最后一个
    "to_data": 0,
    # 是否显示过滤图
    "show_filter": True,
    # "show_filter": False,
    # 是否显示k线图
    # "show_k_line": True,
    "show_k_line": False,

    # 回测功能 参数配置
    "data_handler": {
        'train_ratio': 1,
        'valid_ratio': 0,
        'test_ratio': 0,
    },
    "execution_handler": {
        'initial_capital': 50000,  # 初始资金
        'slippage': 0,  # 滑点比例
        'fee_rate': 0.05,  # 手续费率
        "buy": 1,
        "sell": -1
    },
    "back_tester": {
        'initial_capital': 500000,
        'balance': 5000,
        'exchange': 'binance',
        'contract_type': "swap",
        'lever': '1'
    },

}


# Press the green button in the gutter to run the script.
if __name__ == '__main__':

    results = None
    info_list = []
    ds = DataService()
    # df = ds['ETHUSDT_1h_2020_2025']
    # df = pd.read_csv(
    #     r'C:\Users\<USER>\python sc\example\250416test02.csv')  # 1h

    # df = pd.read_csv(
    #     r'C:\Users\<USER>\python sc\example\回测结果250411_1.csv')  # 15m

    # data = df.fillna(0)

    # execute(
    #     data,
    #     engine_type='two_way',
    # )

    currency = 'ETHUSDT_1h_2020_2025'
    signals_list = ["ret_stc_sig_price",
                    "ret_hv_ratio_signals",
                    "ret_td_signals",
                    "ret_ao_signals",
                    "ret_ena_signals",
                    "ret_williams_r_sig_price",
                    "ret_momentum_sig_price",
                    "ret_kc_strategy",
                    "ret_bollinger_rsi_signals",
                    "ret_macd_sig_price",
                    "ret_ma_arrangement_sig",
                    "ret_ma20_ma120_cross_sig_price",
                    "ret_rsi_ma120_cross_sig_price",
                    "ret_ma120_macd_1_cross_sig_price",
                    "ret_ma120_bolling_cross_sig_price",
                    "ret_ma120_cci_cross_sig_price",
                    "ret_macd_02_cross_sig_price",
                    "ret_ma120_macd_02_cross_sig_price",
                    "ret_cci_fibonacci_signals",
                    "ret_ma20_volume_cross_signals",
                    "ret_ma20_rsi_macd_cross_sig_price",
                    "ret_ma50_cross_sig_price",
                    "ret_ma_bbi_rsi_sig_price",
                    "ret_dc_bbi_cross_sig_price",
                    "ret_ma_cci_sig",
                    "ret_ma_vol_cci_sig",
                    "ret_ma_short_long_cross_sig_price",
                    "ret_ma_atr_cross_sig_price",
                    "ret_dpo_ma_cross_sig_price",
                    "ret_po_signals",
                    "ret_rma_cross_sig_price",
                    "ret_ma120_bbi_signals",
                    "ret_skdj_sig_price",
                    "ret_vao_signals",
                    "ret_wma_signals",
                    "ret_rsi_bb_ma_signal",
                    "ret_macd_cross_signal",
                    "ret_rsi_boll_sig",
                    "ret_mfi_sig_price",
                    ]
    # signals_list = ['ret_stc_sig_price']
    for signal in signals_list:
        df = ds[currency]
        df['signals'] = ds[currency, signal]
        df = df['2021-10-01':]
        df['return_col'] = df['close'].shift(-8)/df['close']-1
        print("K线数据数据总条数:", len(df))

        data = df.fillna(0)

        execute(
            data,
            # engine_type='two_way',
            engine_type=None,
        )

        info = {}

        info['因子名'] = signal
        info['收益率'] = results['total_profit']
        info['最大回撤'] = results['maximum_drawdown']
        info['盈亏比'] = results['loss_ratio']
        info['胜率'] = results['win_rate']
        info['信号覆盖率'] = (df['signals'] != 0).mean()
        info['IC'] = df['signals'].corr(df['return_col'])
        info_list.append(info)

    df_info = pd.DataFrame(info_list)
    # print(df_info)
    df_info.to_csv(r'C:\Users\<USER>\python sc\example\temp2_1h.csv')
    # execute(
    #     data,
    #     engine_type='one_way'
    # )
