import requests
from datetime import datetime
from sqlalchemy import create_engine
import pandas as pd

MYSQL_CONFIG = {
    "host": "************",
    "user": "root",
    "password": "1234",
    "database": "ctadata"
}


class DataManager():

    def __init__(self):
        # self.__tim__ = ticker.TickerInfoManager()

        self.problem_ticker = []
        self.engine = create_engine(f"mysql+pymysql://{MYSQL_CONFIG['user']}:{
            MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}/{MYSQL_CONFIG['database']}")
        return

    def concat_data(self, data):
        """读取并合并单品类永续合约的K线数据"""
        query = f"SELECT * FROM {data.contract}"
        df = pd.read_sql(query, self.engine)
        df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
        df.set_index('open_time', inplace=True)
        # 将所有K线数据合并
        data.data = df
        return


class Data():

    def __init__(self, contract):
        self.contract = contract
        return


currency = 'BTCUSDT_1h_2020_2025'
d = Data(currency)
dm = DataManager()
dm.concat_data(d)
df = d.data


url1 = 'https://fapi.binance.com/fapi/v1/klines'
url2 = 'https://fapi.binance.com/fapi/v1/fundingRate'
params1 = {
    "symbol": 'BTCUSDT',
    'interval': '30m',
    'limit': 1500,
}
params2 = {
    'symbol': 'BTCUSDT',
    'limit': 1000
}
resp = requests.get(url=url1, params=params1)
print(resp.url)
resp_json = resp.json()


for item in resp_json:
    item['fundingTime'] = datetime.utcfromtimestamp(item['fundingTime']//1000)

funding_rate = []
for item in resp_json:
    funding_rate.append(item['fundingRate'])


a = max(funding_rate)
b = min(funding_rate)
