"""
K线序列管理工具，负责：
1. 维护K线序列（固定长度的numpy数组）
2. 计算技术指标
"""

from typing import Optional, Sequence, Dict, Tuple
import numpy as np


class ArrayManager:
    """
    K线序列管理工具，负责：
    1. 维护K线序列（固定长度的numpy数组）
    2. 计算技术指标
    """

    def __init__(self, size: int = 100):
        """
        初始化

        参数:
        size: int
            数组大小
        """
        self.count: int = 0  # 当前K线数量
        self.size: int = size  # 数组大小
        self.inited: bool = False  # 是否已经初始化

        # K线序列，使用numpy数组存储
        self.open_array: np.ndarray = np.zeros(size)
        self.high_array: np.ndarray = np.zeros(size)
        self.low_array: np.ndarray = np.zeros(size)
        self.close_array: np.ndarray = np.zeros(size)
        self.volume_array: np.ndarray = np.zeros(size)

        # 缓存计算结果，避免重复计算
        self._cache: Dict[str, np.ndarray] = {}

        # 预先计算的权重数组，用于快速计算MA
        self._ma_weights = {}

    def update_bar(self, open_price: float, high_price: float, low_price: float, close_price: float, volume: float) -> None:
        """
        更新K线数据 - 使用环形缓冲区实现，避免数组整体移动

        参数:
        open_price: float
            开盘价
        high_price: float
            最高价
        low_price: float
            最低价
        close_price: float
            收盘价
        volume: float
            成交量
        """
        # 清除缓存，因为数据已更新
        self._cache.clear()

        # 使用环形缓冲区实现，避免数组整体移动
        # 计算当前写入位置
        pos = self.count % self.size

        # 添加最新的K线
        self.open_array[pos] = open_price
        self.high_array[pos] = high_price
        self.low_array[pos] = low_price
        self.close_array[pos] = close_price
        self.volume_array[pos] = volume

        # 更新计数器
        self.count += 1
        if self.count >= self.size:
            self.inited = True

    def _get_latest_array(self, array: np.ndarray) -> np.ndarray:
        """
        获取按时间顺序排列的最新数组

        参数:
        array: np.ndarray
            原始数组

        返回:
        np.ndarray: 按时间顺序排列的数组
        """
        if self.count <= self.size:
            # 如果数据不足一个完整的窗口，只返回有效数据
            return array[:self.count]
        else:
            # 使用环形缓冲区逻辑重新排列数组
            pos = self.count % self.size
            return np.concatenate((array[pos:], array[:pos]))

    def get_ma(self, n: int, array: Optional[np.ndarray] = None) -> np.ndarray:
        """
        计算简单移动平均线 - 使用滑动窗口和向量化操作

        参数:
        n: int
            周期
        array: np.ndarray
            数据数组，默认为收盘价

        返回:
        np.ndarray: 移动平均线
        """
        # 检查缓存
        cache_key = f"ma_{n}"
        if array is None and cache_key in self._cache:
            return self._cache[cache_key]

        if array is None:
            # 获取按时间顺序排列的收盘价数组
            array = self._get_latest_array(self.close_array)

        # 使用numpy的卷积操作计算移动平均线
        if n not in self._ma_weights:
            self._ma_weights[n] = np.ones(n) / n

        # 使用快速卷积计算
        result = np.zeros_like(array)
        valid_count = len(array)

        if valid_count >= n:
            # 使用cumsum方法计算滑动窗口平均值，比卷积更快
            cumsum = np.cumsum(np.insert(array, 0, 0))
            result[n-1:] = (cumsum[n:] - cumsum[:-n]) / n
        elif valid_count > 0:
            # 数据不足一个完整周期时，使用可用数据计算均值
            # 这样可以确保短期均线在数据不足时也能正确计算
            result[-1] = np.mean(array)

        # 缓存结果
        if array is None:
            self._cache[cache_key] = result

        return result

    def get_ema(self, n: int, array: Optional[np.ndarray] = None) -> np.ndarray:
        """
        计算指数移动平均线 - 使用向量化操作

        参数:
        n: int
            周期
        array: np.ndarray
            数据数组，默认为收盘价

        返回:
        np.ndarray: 指数移动平均线
        """
        # 检查缓存
        cache_key = f"ema_{n}"
        if array is None and cache_key in self._cache:
            return self._cache[cache_key]

        if array is None:
            # 获取按时间顺序排列的收盘价数组
            array = self._get_latest_array(self.close_array)

        # 计算EMA
        alpha = 2 / (n + 1)
        result = np.zeros_like(array)

        # 处理有效数据
        valid_count = len(array)
        if valid_count > 0:
            result[0] = array[0]

            # 使用向量化操作计算EMA
            for i in range(1, valid_count):
                result[i] = alpha * array[i] + (1 - alpha) * result[i-1]

        # 缓存结果
        if array is None:
            self._cache[cache_key] = result

        return result

    def get_std(self, n: int, array: Optional[np.ndarray] = None) -> np.ndarray:
        """
        计算标准差 - 使用滑动窗口和向量化操作

        参数:
        n: int
            周期
        array: np.ndarray
            数据数组，默认为收盘价

        返回:
        np.ndarray: 标准差
        """
        # 检查缓存
        cache_key = f"std_{n}"
        if array is None and cache_key in self._cache:
            return self._cache[cache_key]

        if array is None:
            # 获取按时间顺序排列的收盘价数组
            array = self._get_latest_array(self.close_array)

        # 计算标准差 - 使用更高效的向量化操作
        result = np.zeros_like(array)
        valid_count = len(array)

        if valid_count >= n:
            # 使用滑动窗口计算标准差 - 优化版本
            # 预先计算平方和，避免重复计算
            squared = array ** 2
            window_sum = np.zeros(valid_count - n + 1)
            window_sum_sq = np.zeros(valid_count - n + 1)

            # 计算第一个窗口的和与平方和
            window_sum[0] = np.sum(array[:n])
            window_sum_sq[0] = np.sum(squared[:n])

            # 使用滑动窗口高效计算后续窗口的和与平方和
            for i in range(1, len(window_sum)):
                window_sum[i] = window_sum[i-1] - array[i-1] + array[i+n-1]
                window_sum_sq[i] = window_sum_sq[i-1] - \
                    squared[i-1] + squared[i+n-1]

            # 计算标准差：sqrt(E[X^2] - E[X]^2)
            mean = window_sum / n
            mean_sq = window_sum_sq / n
            variance = mean_sq - mean ** 2
            # 处理可能的数值误差导致的负方差
            variance = np.maximum(variance, 0)
            std = np.sqrt(variance)

            # 填充结果数组
            result[n-1:] = std

        # 缓存结果
        if array is None:
            self._cache[cache_key] = result

        return result

    def get_boll(self, n: int = 20, dev: float = 2.0, array: Optional[np.ndarray] = None) -> tuple:
        """
        计算布林带

        参数:
        n: int
            周期
        dev: float
            标准差倍数
        array: np.ndarray
            数据数组，默认为收盘价

        返回:
        tuple: (上轨, 中轨, 下轨)
        """
        # 检查缓存
        cache_key = f"boll_{n}_{dev}"
        if array is None and cache_key in self._cache:
            return self._cache[cache_key]

        if array is None:
            # 获取按时间顺序排列的收盘价数组
            array = self._get_latest_array(self.close_array)

        # 计算布林带 - 一次性计算所有值，避免多次调用
        valid_count = len(array)

        if valid_count >= n:
            # 计算移动平均线
            cumsum = np.cumsum(np.insert(array, 0, 0))
            mid = np.zeros_like(array)
            mid[n-1:] = (cumsum[n:] - cumsum[:-n]) / n

            # 计算标准差
            squared = array ** 2
            window_sum = np.zeros(valid_count - n + 1)
            window_sum_sq = np.zeros(valid_count - n + 1)

            # 计算第一个窗口的和与平方和
            window_sum[0] = np.sum(array[:n])
            window_sum_sq[0] = np.sum(squared[:n])

            # 使用滑动窗口高效计算后续窗口的和与平方和
            for i in range(1, len(window_sum)):
                window_sum[i] = window_sum[i-1] - array[i-1] + array[i+n-1]
                window_sum_sq[i] = window_sum_sq[i-1] - \
                    squared[i-1] + squared[i+n-1]

            # 计算标准差
            mean = window_sum / n
            mean_sq = window_sum_sq / n
            variance = mean_sq - mean ** 2
            variance = np.maximum(variance, 0)  # 处理数值误差
            std = np.zeros_like(array)
            std[n-1:] = np.sqrt(variance)

            # 计算布林带
            up = mid + std * dev
            down = mid - std * dev
        else:
            # 数据不足，返回全零数组
            mid = np.zeros_like(array)
            up = np.zeros_like(array)
            down = np.zeros_like(array)

        # 缓存结果
        if array is None:
            self._cache[cache_key] = (up, mid, down)

        return up, mid, down
