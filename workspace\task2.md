# 因子评估
## 1.因子和未来收益率的相关系数(IC值)绝对值超过0.01
## 2.把因子和未来收益率分为N份，观察各组间相关系数是否稳定，也可以用N组的均值除以标准差,得到简单夏普(IR值)，目前我自己看的是夏普绝对值是否超过0.5
## 3.做头尾部超额，将因子排序后取前10%(20%)和后10%(20%)，计算头部的收益率和尾部的收益率，用数量(count)乘以均值(mean)就是头尾部能够带来的超额收益，通常来说头部大于0，尾部小于0代表在因子极端值的情况下都能带来超额收益。
## 4.观察因子分布，做单因子时，可以只看头尾部的超额。但是放入模型中的时候，不能只看头尾部的超额，还要看中间部分能否带来稳定的超额。首先观察因子的分布情况，最好是正态分布，这样出来的因子更符合大数定律，更具有可解释性，表明因子是稳健的。
## 5.如果因子不是正态分布的话，可以尝试加算子(对因子平方，开方、自然对数等)的方式，调整因子分布情况，让因子分布尽可能向正态分布靠近。也可以去掉因子中一些极端值的情况，比如某些因子值偏离均值三个标准差的情况，就可以把因子删除，重新评估因子的整体情况，如果删除极端值之后，因子的整体超额效果变差了比较多，就说明这个因子本身能够带来的超额部分主要是在头尾部
## 6.另外如果因子分布不太好调整的话，可以观察因子和未来收益率的散点图，将图片按因子值划分为几个区间，观察每个区间的describe，计算count*mean，如果绝大多数区间计算出来的结果都是同向的(均大于零或者均小于零)，说明这个因子除了头尾部，中间部分也可以产生稳定的超额收益。