from iid import TimeSeriesHomogeneityTest
from group_stats import GroupStats
import pandas as pd


class FactorEvaluator:
    def __init__(self, df, factor_col='signals', return_col='r96'):
        """初始化时加载所有必要组件"""
        self.df = df.dropna().copy()
        self.factor_col = factor_col
        self.return_col = return_col
        self._validate_columns()

        # 初始化计算组件
        self.ts_test = TimeSeriesHomogeneityTest(self.df[factor_col])
        self.analyzer = GroupStats(self.df, factor_col, return_col)

        # 结果存储结构
        self.results = {
            'iid': {'pvalues': None, 'mean_p': None},
            'corr': {'pearson': None, 'spearman': None},
            'groups': {'stats': None, 'fig': None}
        }

    def run_iid_test(self, window_size=672):
        """独立运行IID检验"""
        pvalues = self.ts_test.hypothesis_test(window_size)
        self.results['iid'] = {
            'pvalues': pvalues,
            'mean_p': pd.Series(pvalues).mean()
        }
        return self  # 支持链式调用

    def calc_correlations(self):
        """独立计算相关系数"""
        self.results['corr'] = {
            'pearson': self.df[self.factor_col].corr(self.df[self.return_col]),
            'spearman': self.df[self.factor_col].corr(self.df[self.return_col], method='spearman')
        }
        return self

    def analyze_groups(self, n_groups=10, show_plot=False):
        """独立执行分组分析"""
        stats, fig = self.analyzer.analyze_groups(n_groups, show_plot)
        self.results['groups'] = {'stats': stats, 'fig': fig}
        return self

    def run_all(self, **kwargs):
        """组合调用各独立方法"""
        return (self.run_iid_test(kwargs.get('window_size', 672))
                .calc_correlations()
                .analyze_groups(kwargs.get('n_groups', 10)))

    @property
    def report(self):
        """统一报告接口"""
        return {
            'IID检验': self.results['iid']['mean_p'],
            'Pearson': self.results['corr']['pearson'],
            'Spearman': self.results['corr']['spearman'],
            '分组收益': self.results['groups']['stats']['return_mean'].tolist()
        }
