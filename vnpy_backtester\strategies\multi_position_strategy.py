"""
多仓位信号策略 - 根据signals列的值进行交易
当signals=1时加多，当signals=-1时加空，每个仓位持仓96根K线后平仓
每个仓位单独记录，可以同时持有多个多单和空单
"""

from typing import List, Tuple
from datetime import datetime
from vnpy_backtester.templates.template import StrategyTemplate
from vnpy_backtester.objects.object import BarData
from vnpy_backtester.utils.log_engine import log_engine


class Position:
    """
    持仓类 - 用于记录每个持仓的信息
    """

    def __init__(self, direction: str, price: float, volume: float, entry_time: datetime):
        """
        初始化持仓

        参数:
        direction: 方向，"long"或"short"
        price: 开仓价格
        volume: 开仓数量
        entry_time: 开仓时间
        """
        self.direction = direction  # "long"或"short"
        self.price = price  # 开仓价格
        self.volume = volume  # 开仓数量
        self.entry_time = entry_time  # 开仓时间
        self.bar_count = 0  # 持仓K线计数


class MultiPositionStrategy(StrategyTemplate):
    """
    多仓位信号策略

    根据signals列的值进行交易:
    - 当signals=1时加多
    - 当signals=-1时加空
    - 每个仓位持仓指定数量的K线后平仓（默认96根，可通过holding_bars参数配置）
    - 每个仓位单独记录，可以同时持有多个多单和空单
    """

    # 策略参数
    holding_bars = 96  # 持仓K线数
    position_size = 1  # 每次开仓的数量或金额
    size = 1  # 合约乘数
    commission_rate = 0.0003  # 默认手续费率，将从引擎获取
    slippage_rate = 0.01  # 默认滑点率，将从引擎获取
    order_type = "quantity"  # 下单方式，可选"quantity"(按币数量)或"amount"(按金额)
    max_positions = None  # 最大持仓数限制，None表示无限制

    # 策略变量
    positions = []  # 持仓列表，每个元素是一个Position对象

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.positions = []  # 初始化持仓列表

        # 从设置中获取持仓K线数量
        if "holding_bars" in self.setting:
            self.holding_bars = self.setting["holding_bars"]
            self.write_log(f"设置持仓K线数量: {self.holding_bars}")

        # 从设置中获取下单方式
        if "order_type" in self.setting:
            self.order_type = self.setting["order_type"]
            self.write_log(f"设置下单方式: {self.order_type}")

        # 从设置中获取仓位大小
        if "position_size" in self.setting:
            self.position_size = self.setting["position_size"]
            if self.order_type == "quantity":
                self.write_log(f"设置每次开仓数量: {self.position_size}")
            else:  # order_type == "amount"
                self.write_log(f"设置每次开仓金额: {self.position_size}")

        # 从设置中获取最大持仓数限制
        if "max_positions" in self.setting:
            self.max_positions = self.setting["max_positions"]
            if self.max_positions is None:
                self.write_log("设置最大持仓数: 无限制")
            else:
                self.write_log(f"设置最大持仓数: {self.max_positions}")

        # 从引擎获取手续费率和滑点率
        if hasattr(self, "engine"):
            self.commission_rate = self.engine.rate
            self.slippage_rate = self.engine.slippage
            self.size = self.engine.size
            self.write_log(
                f"从引擎获取参数 - 手续费率: {self.commission_rate}, 滑点率: {self.slippage_rate}, 合约乘数: {self.size}")

    def on_init(self):
        """
        策略初始化
        """
        self.write_log("多仓位信号策略初始化")

        # 从引擎获取手续费率和滑点率
        if hasattr(self, "engine"):
            self.commission_rate = self.engine.rate
            self.slippage_rate = self.engine.slippage
            self.size = self.engine.size
            self.write_log(
                f"从引擎获取参数 - 手续费率: {self.commission_rate}, 滑点率: {self.slippage_rate}, 合约乘数: {self.size}")

    def on_start(self):
        """
        策略启动
        """
        self.write_log("多仓位信号策略启动")

    def on_stop(self):
        """
        策略停止
        """
        self.write_log("多仓位信号策略停止")

    def write_log(self, msg, level="INFO"):
        """
        写入日志

        参数:
        msg: 日志消息
        level: 日志级别，可以是"DEBUG", "INFO", "WARNING", "ERROR"
        """
        # 只有在非DEBUG级别或者DEBUG模式开启时才记录日志
        if level != "DEBUG" or getattr(self, "debug_mode", False):
            # 使用日志引擎写入日志
            log_engine.write_log(f"{self.strategy_name} - {msg}")

            # 同时调用父类的write_log方法
            super().write_log(msg)

    def get_available_capital(self):
        """
        获取可用资金

        返回:
        float: 可用资金
        """
        # 缓存计算结果，避免频繁重复计算
        if hasattr(self, '_last_bar_datetime') and self._last_bar_datetime == self.bar.datetime:
            return self._cached_available_capital

        # 从引擎获取初始资金
        initial_capital = 50000.0  # 默认值
        if hasattr(self, "engine") and hasattr(self.engine, "capital"):
            initial_capital = self.engine.capital

        # 使用累计方式计算已使用资金，而不是每次都重新计算
        if not hasattr(self, '_used_capital'):
            # 首次计算
            self._used_capital = 0.0
            for position in self.positions:
                self._used_capital += position.price * position.volume

        # 计算可用资金
        available_capital = initial_capital - self._used_capital

        # 确保可用资金不小于0
        available_capital = max(0.0, available_capital)

        # 缓存计算结果
        self._last_bar_datetime = self.bar.datetime
        self._cached_available_capital = available_capital

        return available_capital

    def on_bar(self, bar: BarData):
        """
        K线更新回调
        """
        # 保存当前K线对象，供其他方法使用
        self.bar = bar

        # 获取信号值
        signal = getattr(bar, "signals", 0)

        # 检查现有持仓，是否需要平仓
        # 使用列表推导式代替循环和条件判断，提高效率
        positions_to_close = []
        positions_to_keep = []

        for position in self.positions:
            # 增加持仓K线计数
            position.bar_count += 1

            # 根据持仓时间分类
            if position.bar_count >= self.holding_bars:
                positions_to_close.append(position)
            else:
                positions_to_keep.append(position)

        # 使用类中定义的手续费率、滑点率和合约乘数

        # 批量处理需要平仓的持仓
        for position in positions_to_close:
            if position.direction == "long":
                # 计算多头盈亏
                profit = (bar.close_price - position.price) * position.volume
                profit_pct = (bar.close_price / position.price - 1) * 100

                # 计算平仓时的手续费和滑点
                close_commission = position.volume * self.size * \
                    bar.close_price * self.commission_rate

                # 滑点计算 - 使用固定点数方式
                close_slippage = position.volume * self.size * self.slippage_rate  # 固定点数方式

                # 计算开仓时的手续费和滑点（回溯计算）
                open_commission = position.volume * self.size * \
                    position.price * self.commission_rate

                # 滑点计算 - 使用固定点数方式
                open_slippage = position.volume * self.size * self.slippage_rate  # 固定点数方式

                # 计算总手续费和总滑点
                total_commission = open_commission + close_commission
                total_slippage = open_slippage + close_slippage

                # 计算净盈亏（包含开仓和平仓的所有成本）
                net_profit = profit - total_commission - total_slippage

                # 平仓
                self.sell(bar.close_price, position.volume)

                # 更新已使用资金
                if hasattr(self, '_used_capital'):
                    self._used_capital -= position.price * position.volume

                # 记录平仓信息和盈亏
                self.write_log(
                    f"时间平多：{bar.datetime}, 价格：{bar.close_price}, 数量：{position.volume}, 开仓价：{position.price}, 持仓K线数：{position.bar_count}, 盈亏：{profit:.2f}, 开仓手续费：{open_commission:.2f}, 平仓手续费：{close_commission:.2f}, 滑点：{total_slippage:.2f}, 净盈亏：{net_profit:.2f} ({profit_pct:.2f}%)")
            else:  # direction == "short"
                # 计算空头盈亏
                profit = (position.price - bar.close_price) * position.volume
                profit_pct = (position.price / bar.close_price - 1) * 100

                # 计算平仓时的手续费和滑点
                close_commission = position.volume * self.size * \
                    bar.close_price * self.commission_rate

                # 滑点计算 - 使用固定点数方式
                close_slippage = position.volume * self.size * self.slippage_rate  # 固定点数方式

                # 计算开仓时的手续费和滑点（回溯计算）
                open_commission = position.volume * self.size * \
                    position.price * self.commission_rate

                # 滑点计算 - 使用固定点数方式
                open_slippage = position.volume * self.size * self.slippage_rate  # 固定点数方式

                # 计算总手续费和总滑点
                total_commission = open_commission + close_commission
                total_slippage = open_slippage + close_slippage

                # 计算净盈亏（包含开仓和平仓的所有成本）
                net_profit = profit - total_commission - total_slippage

                # 平仓
                self.cover(bar.close_price, position.volume)

                # 更新已使用资金
                if hasattr(self, '_used_capital'):
                    self._used_capital -= position.price * position.volume

                # 记录平仓信息和盈亏
                self.write_log(
                    f"时间平空：{bar.datetime}, 价格：{bar.close_price}, 数量：{position.volume}, 开仓价：{position.price}, 持仓K线数：{position.bar_count}, 盈亏：{profit:.2f}, 开仓手续费：{open_commission:.2f}, 平仓手续费：{close_commission:.2f}, 滑点：{total_slippage:.2f}, 净盈亏：{net_profit:.2f} ({profit_pct:.2f}%)")

        # 更新持仓列表 - 直接使用保留的持仓列表，避免再次创建新列表
        self.positions = positions_to_keep

        # 开仓逻辑 - 根据信号开仓
        # 获取当前可用资金
        available_capital = self.get_available_capital()

        # 计算开仓数量和所需资金
        margin_rate = 1.0  # 全额保证金

        if self.order_type == "quantity":
            # 按币数量下单
            volume = self.position_size
            # 计算开仓所需资金
            required_capital = bar.close_price * volume * margin_rate
            # 计算手续费
            commission = required_capital * self.commission_rate
            # 计算滑点 - 使用固定点数方式
            slippage = volume * self.size * self.slippage_rate
        else:  # order_type == "amount"
            # 按金额下单
            amount = self.position_size
            # 计算可以买入的币数量（考虑手续费和滑点）
            # 解方程：price * volume + price * volume * commission_rate + volume * slippage_rate = amount
            # 简化为：volume * price * (1 + commission_rate) + volume * slippage_rate = amount
            # 得到：volume = amount / (price * (1 + commission_rate) + slippage_rate)
            volume = amount / (bar.close_price *
                               (1 + self.commission_rate) + self.slippage_rate)
            # 四舍五入到4位小数
            volume = round(volume, 4)
            # 计算实际所需资金
            required_capital = amount
            # 计算手续费和滑点
            commission = bar.close_price * volume * self.commission_rate
            slippage = volume * self.size * self.slippage_rate

        # 加上手续费和滑点
        total_cost = required_capital + commission + slippage

        # 检查是否接近回测结束
        # 获取剩余K线数量（由引擎设置在bar对象中）
        remaining_bars = getattr(bar, 'remaining_bars', 0)

        # 添加调试日志，记录每个K线的剩余K线数量
        if remaining_bars <= self.holding_bars + 5:  # 只记录接近结束的K线
            self.write_log(
                f"当前K线：{bar.datetime}, 剩余K线数：{remaining_bars}, 持仓周期：{self.holding_bars}, 信号：{signal}",
                level="DEBUG")

        # 如果剩余K线数量少于持仓周期，不开新仓
        if remaining_bars < self.holding_bars:
            if signal != 0:  # 有信号但不开仓
                self.write_log(
                    f"接近回测结束，不开新仓：{bar.datetime}, 剩余K线数：{remaining_bars}, 持仓周期：{self.holding_bars}, 信号：{signal}")
            return  # 直接返回，不执行开仓逻辑

        # 检查最大持仓数限制
        current_positions = len(self.positions)
        if self.max_positions is not None and current_positions >= self.max_positions:
            if signal != 0:  # 有信号但不开仓
                self.write_log(
                    f"达到最大持仓数限制，不开新仓：{bar.datetime}, 当前持仓数：{current_positions}, 最大持仓数：{self.max_positions}, 信号：{signal}")
            return  # 直接返回，不执行开仓逻辑

        if signal == 1:  # 开多
            if available_capital >= total_cost:
                # 资金充足，可以开仓
                self.buy(bar.close_price, volume)
                # 创建新的持仓对象并添加到持仓列表
                new_position = Position(
                    "long", bar.close_price, volume, bar.datetime)
                self.positions.append(new_position)
                # 更新已使用资金
                if hasattr(self, '_used_capital'):
                    self._used_capital += bar.close_price * volume

                # 记录日志
                if self.order_type == "quantity":
                    self.write_log(
                        f"做多(按数量)：{bar.datetime}, 价格：{bar.close_price}, 数量：{volume}, 信号：{signal}, 手续费：{commission:.2f}, 滑点：{slippage:.2f}")
                else:
                    self.write_log(
                        f"做多(按金额)：{bar.datetime}, 价格：{bar.close_price}, 金额：{self.position_size}, 数量：{volume}, 信号：{signal}, 手续费：{commission:.2f}, 滑点：{slippage:.2f}")
            else:
                # 资金不足，记录到日志
                if self.order_type == "quantity":
                    self.write_log(
                        f"资金不足，无法做多：{bar.datetime}, 价格：{bar.close_price}, 数量：{volume}, 信号：{signal}, 所需资金：{total_cost:.2f}, 可用资金：{available_capital:.2f}")
                else:
                    self.write_log(
                        f"资金不足，无法做多：{bar.datetime}, 价格：{bar.close_price}, 金额：{self.position_size}, 数量：{volume}, 信号：{signal}, 所需资金：{total_cost:.2f}, 可用资金：{available_capital:.2f}")

        elif signal == -1:  # 开空
            if available_capital >= total_cost:
                # 资金充足，可以开仓
                self.short(bar.close_price, volume)
                # 创建新的持仓对象并添加到持仓列表
                new_position = Position(
                    "short", bar.close_price, volume, bar.datetime)
                self.positions.append(new_position)
                # 更新已使用资金
                if hasattr(self, '_used_capital'):
                    self._used_capital += bar.close_price * volume

                # 记录日志
                if self.order_type == "quantity":
                    self.write_log(
                        f"做空(按数量)：{bar.datetime}, 价格：{bar.close_price}, 数量：{volume}, 信号：{signal}, 手续费：{commission:.2f}, 滑点：{slippage:.2f}")
                else:
                    self.write_log(
                        f"做空(按金额)：{bar.datetime}, 价格：{bar.close_price}, 金额：{self.position_size}, 数量：{volume}, 信号：{signal}, 手续费：{commission:.2f}, 滑点：{slippage:.2f}")
            else:
                # 资金不足，记录到日志
                if self.order_type == "quantity":
                    self.write_log(
                        f"资金不足，无法做空：{bar.datetime}, 价格：{bar.close_price}, 数量：{volume}, 信号：{signal}, 所需资金：{total_cost:.2f}, 可用资金：{available_capital:.2f}")
                else:
                    self.write_log(
                        f"资金不足，无法做空：{bar.datetime}, 价格：{bar.close_price}, 金额：{self.position_size}, 数量：{volume}, 信号：{signal}, 所需资金：{total_cost:.2f}, 可用资金：{available_capital:.2f}")
