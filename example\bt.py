import pandas as pd
import numpy as np
from typing import Optional, Union


class QuickBacktest:
    """
    一个快速、灵活的回测引擎，用于基于持仓信号的策略回测

    参数:
        data (pd.DataFrame): 包含价格、持仓和交易成本数据的DataFrame
        price_col (str): 价格列名，默认为'close'
        volume_col (str): 成交量列名，可选
        position_col (str): 持仓信号列名，默认为'position'
        fee (Union[str, float]): 手续费列名或固定值，默认为'fee'
        slippage (Union[str, float]): 滑点列名或固定值，默认为'slippage'
        trade_at (str): 交易执行价格，'open'或'close'，默认为'close'
    """

    def __init__(self,
                 data: pd.DataFrame,
                 price_col: str = 'close',
                 volume_col: Optional[str] = None,
                 position_col: str = 'position',
                 fee: Union[str, float] = 'fee',
                 slippage: Union[str, float] = 'slippage',
                 trade_at: str = 'close'):

        self.data = data.copy()
        self.price_col = price_col
        self.volume_col = volume_col
        self.position_col = position_col
        self.fee = fee
        self.slippage = slippage
        self.trade_at = trade_at
        self.results = None

        # 验证输入
        self._validate_inputs()

    def _validate_inputs(self):
        """验证输入参数的有效性"""
        if self.price_col not in self.data:
            raise ValueError(
                f"Price column '{self.price_col}' not found in data")

        if self.position_col not in self.data:
            raise ValueError(
                f"Position column '{self.position_col}' not found in data")

        if isinstance(self.fee, str) and self.fee not in self.data:
            raise ValueError(f"Fee column '{self.fee}' not found in data")

        if isinstance(self.slippage, str) and self.slippage not in self.data:
            raise ValueError(
                f"Slippage column '{self.slippage}' not found in data")

        if self.trade_at not in ['open', 'close']:
            raise ValueError("trade_at must be either 'open' or 'close'")

    def run_backtest(self, initial_capital: float = 1.0) -> pd.DataFrame:
        """
        运行回测

        参数:
            initial_capital (float): 初始资金，默认为1.0

        返回:
            pd.DataFrame: 包含回测结果的DataFrame
        """
        df = self.data.copy()

        # 填充仓位缺失值
        df[self.position_col] = df[self.position_col].fillna(
            method='ffill').fillna(0)

        # 确保仓位是数值类型
        df[self.position_col] = pd.to_numeric(
            df[self.position_col], errors='coerce').fillna(0)

        # 计算前一日仓位
        df['prev_position'] = df[self.position_col].shift(1).fillna(0)

        # 计算仓位变化
        df['position_chg'] = df[self.position_col] - df['prev_position']

        # 处理交易成本
        if isinstance(self.fee, str):
            fee = df[self.fee]
        else:
            fee = self.fee

        if isinstance(self.slippage, str):
            slippage = df[self.slippage]
        else:
            slippage = self.slippage

        # 计算交易成本（绝对值）
        df['transaction_cost'] = abs(df['position_chg']) * (fee + slippage)

        # 计算价格变化
        df['price_change'] = df[self.price_col].diff().fillna(0)

        # 计算持仓收益
        df['holding_pnl'] = df['prev_position'] * df['price_change']

        # 计算每日净收益
        df['daily_pnl'] = df['holding_pnl'] - df['transaction_cost']

        # 计算累计收益
        df['cumulative_pnl'] = df['daily_pnl'].cumsum()

        # 计算权益曲线
        df['equity_curve'] = initial_capital + df['cumulative_pnl']

        # 计算每日收益率
        df['daily_return'] = df['daily_pnl'] / initial_capital

        # 计算累计收益率
        df['cumulative_return'] = df['equity_curve'] / initial_capital - 1

        # 保存结果
        self.results = df
        return self.results

    def get_results(self) -> Optional[pd.DataFrame]:
        """获取回测结果"""
        return self.results

    def get_performance_metrics(self) -> dict:
        """计算关键绩效指标"""
        if self.results is None:
            raise ValueError(
                "No backtest results available. Run backtest first.")

        df = self.results
        metrics = {}

        # 总收益率
        metrics['total_return'] = df['equity_curve'].iloc[-1] / \
            df['equity_curve'].iloc[0] - 1

        # 年化收益率
        days = len(df)
        metrics['annualized_return'] = (
            1 + metrics['total_return']) ** (252/days) - 1

        # 最大回撤
        peak = df['equity_curve'].cummax()
        drawdown = (df['equity_curve'] - peak) / peak
        metrics['max_drawdown'] = drawdown.min()

        # 夏普比率 (假设无风险利率为0)
        daily_returns = df['daily_return']
        metrics['sharpe_ratio'] = np.sqrt(
            252) * daily_returns.mean() / daily_returns.std()

        # 胜率
        metrics['win_rate'] = len(
            daily_returns[daily_returns > 0]) / len(daily_returns)

        # 平均盈亏比
        winning_trades = daily_returns[daily_returns > 0]
        losing_trades = daily_returns[daily_returns < 0]
        if len(losing_trades) > 0:
            metrics['profit_loss_ratio'] = winning_trades.mean() / \
                abs(losing_trades.mean())
        else:
            metrics['profit_loss_ratio'] = np.inf

        return metrics
