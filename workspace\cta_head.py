# from config import config
from abc import ABC, abstractmethod
from typing import List, NamedTuple
import pandas as pd
from factors_lib.final_factors import FinalFactor  # 因子库的导入
from sqlalchemy import create_engine


# 定义命名元组用于返回值类型


class FactorResult(NamedTuple):
    factor: pd.DataFrame  # 因子数据
    metadata: dict  # 元数据（可选）


class PredictionResult(NamedTuple):
    prediction: int  # 预测结果
    confidence: float  # 置信度（可选）


# factor_name = config['factor_name']
# model_path = config['model_path']


class CTAStrategy:
    def __init__(self, model_path):
        super().__init__()
        # self.model_path = config['model_path']

    @abstractmethod
    def calc_factor(self, factor_name: List[str], kline: pd.DataFrame) -> FactorResult:
        factor_results = {}

        for name in factor_name:
            if hasattr(FinalFactor, name):  # 检查方法是否存在
                factor_method = getattr(FinalFactor, name)  # 获取因子方法
                factor_results[name] = factor_method(kline)  # 计算因子
            else:
                raise ValueError(f"因子 {name} 在库中不存在")

        return FactorResult(factor=pd.DataFrame(factor_results), metadata={"source": "CTAStrategy"})

    @abstractmethod
    def predict(self, factor: pd.DataFrame) -> PredictionResult:
        """
        基于因子数据进行预测。

        :param factor: 因子数据
        :return: 包含预测结果和置信度的命名元组
        """
        pass

    @abstractmethod
    def update_model(self, new_data: pd.DataFrame) -> None:
        """
        更新模型。

        :param new_data: 新数据
        """
        pass


class DataManager():

    def __init__(self):
        # self.__tim__ = ticker.TickerInfoManager()

        self.problem_ticker = []
        self.engine = create_engine(f"mysql+pymysql://{MYSQL_CONFIG['user']}:{
                                    MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}/{MYSQL_CONFIG['database']}")
        return

    def concat_data(self, data):
        """读取并合并单品类永续合约的K线数据"""
        query = f"SELECT * FROM {data.contract}"
        df = pd.read_sql(query, self.engine)
        df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
        df.set_index('open_time', inplace=True)
        # 将所有K线数据合并
        data.data = df
        return


class Data():

    def __init__(self, contract):
        self.contract = contract
        return


def factor_sample_1(d):
    """示例因子1"""
    c = d.close
    long_T = 20
    short_T = 5
    ma_short = c.rolling(window=short_T).mean()
    ma_long = c.rolling(window=long_T).mean()

    s = ma_short / ma_long

    return s


if __name__ == "__main__":

    MYSQL_CONFIG = {
        "host": "************",
        "user": "root",
        "password": "1234",
        "database": "ctadata"
    }

    d = Data('BTCUSDT_15m_2020_2025')
    dm = DataManager()
    dm.concat_data(d)
    d = d.data

    strategy = CTAStrategy(model_path='')
    factor_result = strategy.calc_factor(["factor_sample_1"], d)
    print(factor_result[0], factor_result[1], sep=' ')
