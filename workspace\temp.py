import numpy as np
import matplotlib.pyplot as plt
import pickle
import os
from datetime import datetime
import requests
import pandas as pd
import json

model_result = pd.read_pickle(
    r'C:\Users\<USER>\python sc\example\backtest_check_250417.pkl')
equity_curve_15m = model_result['15m']['equity_curve'].dropna()
equity_curve_1h = model_result['1h']['equity_curve'].dropna()

model_1h = pd.read_csv(r'C:\Users\<USER>\python sc\example\回测结果250417_2.csv')[
    'equity_curve']

real_15m_equity_curve = pd.read_json(
    r'C:\Users\<USER>\python sc\example\15m.txt', orient='index')[0].dropna()
real_15m_equity_curve.index = equity_curve_15m.index
real_1h_equity_curve = pd.read_json(
    r'C:\Users\<USER>\python sc\example\1h.txt', orient='index')[0].dropna()
real_1h_equity_curve.index = model_1h.index
with open(r'C:\Users\<USER>\python sc\example\15m.txt', 'r') as f:
    data = json.load(f)


print(real_1h_equity_curve.corr(model_1h))
print(real_1h_equity_curve.describe())
print(model_1h.describe())


# %%
# 设置代理环境变量（仅限当前Python进程）
os.environ["HTTP_PROXY"] = "http://127.0.0.1:7897"
os.environ["HTTPS_PROXY"] = "http://127.0.0.1:7897"

url = 'https://fapi.binance.com/futures/data/basis'

params = {
    'pair': 'ETHUSDT',
    'contractType': 'PERPETUAL',
    'period': '15m',
    'limit': 500,
}


resp = requests.get(url=url, params=params)
res = resp.json()

for info in res[:30]:
    print(datetime.fromtimestamp(info['timestamp']/1000), info['basis'])


# %%
raw_data = [
    {
        "e": "depthUpdate",
        "E": 1746330581366,
        "T": 1746330581365,
        "s": "ETHUSDT",
        "U": 7421519910192,
        "u": 7421519927614,
        "pu": 7421519908893,
        "b": [
            ["1833.45", "158.578"],
            ["1833.44", "0.012"],
            ["1833.43", "2.193"],
            ["1833.42", "0.027"],
            ["1833.41", "16.397"],
            ["1833.40", "2.270"],
            ["1833.39", "5.420"],
            ["1833.38", "3.728"],
            ["1833.36", "0.046"],
            ["1833.35", "0.545"],
            ["1833.34", "0.039"],
            ["1833.33", "4.571"],
            ["1833.32", "17.939"],
            ["1833.31", "0.038"],
            ["1833.30", "8.697"],
            ["1833.29", "8.062"],
            ["1833.28", "1.376"],
            ["1833.27", "2.735"],
            ["1833.26", "7.506"],
            ["1833.25", "0.024"]
        ],
        "a": [
            ["1833.46", "46.616"],
            ["1833.47", "1.069"],
            ["1833.48", "0.071"],
            ["1833.50", "2.036"],
            ["1833.51", "0.023"],
            ["1833.52", "0.012"],
            ["1833.53", "0.064"],
            ["1833.54", "9.073"],
            ["1833.55", "4.115"],
            ["1833.56", "38.682"],
            ["1833.57", "9.524"],
            ["1833.58", "16.070"],
            ["1833.59", "0.063"],
            ["1833.60", "2.554"],
            ["1833.61", "0.078"],
            ["1833.62", "2.208"],
            ["1833.63", "0.926"],
            ["1833.64", "29.257"],
            ["1833.65", "6.440"],
            ["1833.66", "22.553"]
        ]
    },
    {
        "e": "depthUpdate",
        "E": 1746330581624,
        "T": 1746330581618,
        "s": "ETHUSDT",
        "U": 7421519927954,
        "u": 7421519943925,
        "pu": 7421519927614,
        "b": [
            ["1833.45", "158.777"],
            ["1833.44", "0.027"],
            ["1833.43", "4.867"],
            ["1833.42", "16.372"],
            ["1833.41", "0.037"],
            ["1833.40", "0.041"],
            ["1833.39", "5.420"],
            ["1833.38", "3.728"],
            ["1833.36", "0.046"],
            ["1833.35", "0.545"],
            ["1833.34", "0.039"],
            ["1833.33", "4.571"],
            ["1833.32", "17.939"],
            ["1833.31", "0.038"],
            ["1833.30", "8.697"],
            ["1833.29", "8.062"],
            ["1833.28", "1.376"],
            ["1833.27", "2.735"],
            ["1833.26", "7.506"],
            ["1833.25", "0.024"]
        ],
        "a": [
            ["1833.46", "46.558"],
            ["1833.47", "1.069"],
            ["1833.48", "0.071"],
            ["1833.50", "2.036"],
            ["1833.51", "0.023"],
            ["1833.52", "0.012"],
            ["1833.53", "0.064"],
            ["1833.54", "6.458"],
            ["1833.55", "4.115"],
            ["1833.56", "38.682"],
            ["1833.57", "6.021"],
            ["1833.58", "16.070"],
            ["1833.59", "0.063"],
            ["1833.60", "2.554"],
            ["1833.61", "0.078"],
            ["1833.62", "2.307"],
            ["1833.63", "8.003"],
            ["1833.64", "11.364"],
            ["1833.65", "6.440"],
            ["1833.66", "22.553"]
        ]
    },
    {
        "e": "depthUpdate",
        "E": 1746330581901,
        "T": 1746330581896,
        "s": "ETHUSDT",
        "U": 7421519945235,
        "u": 7421519957513,
        "pu": 7421519943925,
        "b": [
            ["1833.45", "158.770"],
            ["1833.44", "16.919"],
            ["1833.43", "2.193"],
            ["1833.42", "0.012"],
            ["1833.41", "0.037"],
            ["1833.40", "0.041"],
            ["1833.39", "5.420"],
            ["1833.38", "3.728"],
            ["1833.36", "0.046"],
            ["1833.35", "0.545"],
            ["1833.34", "0.039"],
            ["1833.33", "4.571"],
            ["1833.32", "17.939"],
            ["1833.31", "0.038"],
            ["1833.30", "8.697"],
            ["1833.29", "8.062"],
            ["1833.28", "1.376"],
            ["1833.27", "2.735"],
            ["1833.26", "7.506"],
            ["1833.25", "0.024"]
        ],
        "a": [
            ["1833.46", "46.759"],
            ["1833.47", "0.972"],
            ["1833.48", "0.071"],
            ["1833.50", "2.036"],
            ["1833.51", "0.041"],
            ["1833.52", "0.012"],
            ["1833.53", "0.064"],
            ["1833.54", "6.443"],
            ["1833.55", "0.039"],
            ["1833.56", "38.682"],
            ["1833.57", "6.021"],
            ["1833.58", "16.070"],
            ["1833.59", "0.063"],
            ["1833.60", "2.554"],
            ["1833.61", "0.078"],
            ["1833.62", "2.307"],
            ["1833.63", "8.003"],
            ["1833.64", "20.406"],
            ["1833.65", "6.440"],
            ["1833.66", "22.553"]
        ]
    },
    {
        "e": "depthUpdate",
        "E": 1746330582160,
        "T": 1746330582158,
        "s": "ETHUSDT",
        "U": 7421519957853,
        "u": 7421519995747,
        "pu": 7421519957513,
        "b": [
            ["1833.45", "4.354"],
            ["1833.44", "0.012"],
            ["1833.43", "0.012"],
            ["1833.42", "0.012"],
            ["1833.41", "0.025"],
            ["1833.40", "0.026"],
            ["1833.38", "0.012"],
            ["1833.36", "0.046"],
            ["1833.35", "0.545"],
            ["1833.34", "0.024"],
            ["1833.33", "0.732"],
            ["1833.32", "0.024"],
            ["1833.31", "0.311"],
            ["1833.30", "8.347"],
            ["1833.29", "6.262"],
            ["1833.28", "0.012"],
            ["1833.27", "22.631"],
            ["1833.26", "7.494"],
            ["1833.25", "1.388"],
            ["1833.24", "1.846"]
        ],
        "a": [
            ["1833.46", "65.318"],
            ["1833.47", "0.972"],
            ["1833.48", "0.471"],
            ["1833.50", "34.122"],
            ["1833.51", "26.704"],
            ["1833.52", "24.446"],
            ["1833.53", "34.848"],
            ["1833.54", "3.994"],
            ["1833.55", "41.453"],
            ["1833.56", "40.046"],
            ["1833.57", "17.363"],
            ["1833.58", "32.429"],
            ["1833.59", "1.455"],
            ["1833.60", "3.434"],
            ["1833.61", "13.178"],
            ["1833.62", "9.027"],
            ["1833.63", "8.195"],
            ["1833.64", "10.159"],
            ["1833.65", "20.604"],
            ["1833.66", "14.973"]
        ]
    }
]

data = raw_data[0]

ask_list = data['a']
ask_price = [float(x[0]) for x in ask_list]
ask_volume = [float(x[1]) for x in ask_list]

sum(ask_volume)


bid_list = data['b']
bid_price = [float(x[0]) for x in bid_list]
bid_volume = [float(x[1]) for x in bid_list]

sum(bid_volume)


# %%
path = r'C:/Users/<USER>/python sc/factor_evaluation/market_data/ws_data_20250504_165135_10000条.pkl'
with open(path, "rb") as f:
    data = pickle.load(f)

for i in data[1:]:
    i['E'] = datetime.fromtimestamp(i['E']/1000)  # 事件时间
    i['T'] = datetime.fromtimestamp(i['T']/1000)  # 撮合时间
    i['close'] = (float(i['b'][0][0])+float(i['a'][0][0]))/2
    i['ask_price1'] = float(i['a'][0][0])
    i['ask_volume1'] = float(i['a'][0][1])
    i['bid_price1'] = float(i['b'][0][0])
    i['bid_volume1'] = float(i['b'][0][1])
    i['ask_volume_sum'] = sum(float(x[1]) for x in i['a'])
    i['bid_volume_sum'] = sum(float(x[1]) for x in i['b'])
    i['volume_diff'] = i['ask_volume_sum']-i['bid_volume_sum']


# %%

df = pd.DataFrame(data[1:])

# 初始化存储相关系数的列表
correlations = []

# 循环计算r1到r1000与volume_diff的相关系数
for i in range(1, 1001):
    future_return = df['close'].shift(-i) / df['close'] - 1
    corr = future_return.corr(df['volume_diff'])
    correlations.append(corr)

# 创建图表
plt.figure(figsize=(15, 7))

# 绘制折线图
plt.plot(range(1, 1001), correlations,
         color='steelblue', linewidth=1.5, label='相关系数')
# 找出极值点
max_idx = np.argmax(correlations)
# 标记最大值点
plt.scatter(max_idx+1, correlations[max_idx],
            color='red', s=50, zorder=5,
            label=f'最大值 (r{max_idx+1}={correlations[max_idx]:.3f})')
plt.show()
print('最大值索引: ', max_idx)
print('最大值: ', correlations[max_idx])

# %%

cookies = {
    'stel_ssid': '1cc5512a08d475eb4d_14603401704458197111',
}

headers = {
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
    'cache-control': 'max-age=0',
    'priority': 'u=0, i',
    'referer': 'https://t.me/cngg_twap',
    'sec-ch-ua': '"Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'document',
    'sec-fetch-mode': 'navigate',
    'sec-fetch-site': 'same-origin',
    'sec-fetch-user': '?1',
    'upgrade-insecure-requests': '1',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0',
    # 'cookie': 'stel_ssid=1cc5512a08d475eb4d_14603401704458197111',
}

response = requests.get('https://t.me/s/cngg_twap',
                        cookies=cookies, headers=headers)

response.text

# %%
df = pd.read_pickle(
    r'C:\Users\<USER>\python sc\traditional_cta\status\position_state.pkl')
Position = df['positions'][-1]
print(vars(Position))


# %%
from tempp import A
print(A.x,A.y)




