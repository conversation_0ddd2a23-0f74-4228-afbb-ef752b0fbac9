import requests
import time


def send_signal(signal, title):
    url = 'https://tcta.skyfffire.com/api/cta/signal/send/group'
    timestamp = int(time.time())*1000
    params = {
        "timestamp": timestamp,
        "title": title,
        "signal": signal,
    }
    try:
        response = requests.post(url, json=params, timeout=10)
        response.raise_for_status()
        status_code = response.status_code  # 获取状态码
        print(f"✅ 信号发送成功: {signal}")
        print(f"状态码: {status_code}")
    except Exception as e:
        print.error(f"❌ 信号发送失败: {e}")


signal = 0
title = "c_ETH@15min_test"
send_signal(signal, title)
