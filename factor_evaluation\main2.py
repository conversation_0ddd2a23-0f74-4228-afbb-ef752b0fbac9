"""
因子评估系统 - 多币种批量分析
支持类似DataService的调用方式

功能:
1. 使用FactorEvaluator类进行因子分析
2. 将结果保存在字典中，不打印输出
3. 将图表保存为文件
4. 支持多币种批量分析
5. 将结果合并为DataFrame
"""
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from factor_evaluator import FactorEvaluator

# 创建保存结果的目录
os.makedirs('results', exist_ok=True)
os.makedirs('results/plots', exist_ok=True)


def analyze_currency(currency, start_date=None, end_date=None, return_period=96, factor_name='MA_Deviation'):
    """
    分析单个币种的因子表现

    参数:
        currency (str): 币种代码，格式如 'ETHUSDT_15m_2020_2025'
        start_date (str): 起始日期，格式如 '2021-10-1'
        end_date (str): 结束日期，默认为None表示使用所有可用数据
        return_period (int): 收益率计算周期，默认为96（对应15分钟K线的24小时）
        factor_name (str): 因子名称，默认为'MA_Deviation'（均线偏离因子）

    返回:
        dict: 包含分析结果的字典
    """
    # 创建FactorEvaluator实例 - 不显示图表
    fe = FactorEvaluator(return_period=return_period, show_plots=False)

    # 根据提供的参数调用FactorEvaluator
    if start_date is None:
        # 只提供币种
        result = fe[currency]
    elif end_date is None:
        # 提供币种和开始日期
        result = fe[currency, start_date]
    else:
        # 提供币种、开始日期和结束日期
        result = fe[currency, start_date, end_date]

    # 保存分组收益图表
    if 'group_stats' in result and 'group_fig' in result:
        fig = result['group_fig']
        fig_path = f'results/plots/{currency}_group_returns.png'
        fig.savefig(fig_path)
        plt.close(fig)

        # 将图表路径添加到结果中
        result['group_fig_path'] = fig_path
        # 删除图表对象，避免在结果字典中保存大对象
        del result['group_fig']

    # 生成并保存因子分布直方图
    plt.figure(figsize=(10, 6))
    result['df']['signals'].hist(bins=100)
    plt.title(f'Factor Distribution - {currency}')
    plt.xlabel('Factor Value')
    plt.ylabel('Frequency')
    hist_path = f'results/plots/{currency}_factor_hist.png'
    plt.savefig(hist_path)
    plt.close()

    # 将直方图路径添加到结果中
    result['hist_fig_path'] = hist_path

    # 提取关键指标到结果中
    result['currency'] = currency
    result['factor_name'] = factor_name  # 添加因子名
    result['start_date'] = start_date
    result['end_date'] = end_date
    result['return_period'] = return_period

    # 删除DataFrame，避免在结果字典中保存大对象
    if 'df' in result:
        del result['df']

    return result


def analyze_multiple_currencies(currencies=None, start_date='2021-10-1', end_date=None, return_period=96, factor_name='MA_Deviation'):
    """
    批量分析多个币种的因子表现

    参数:
        currencies (list): 币种代码列表，如果为None则使用默认列表
        start_date (str): 起始日期，格式如 '2021-10-1'
        end_date (str): 结束日期，默认为None表示使用所有可用数据
        return_period (int): 收益率计算周期，默认为96（对应15分钟K线的24小时）
        factor_name (str): 因子名称，默认为'MA_Deviation'（均线偏离因子）

    返回:
        dict: 包含所有币种分析结果的字典
        pd.DataFrame: 包含关键指标的DataFrame
    """
    # 如果未提供币种列表，使用默认列表
    if currencies is None:
        currencies = [
            'BTCUSDT_15m_2020_2025',
            'ETHUSDT_15m_2020_2025',
            'BNBUSDT_15m_2020_2025'  # 请确保数据库中有这些币种的数据
        ]

    results = {}
    summary_data = []

    # 循环分析每个币种
    for currency in currencies:
        # 分析币种
        result = analyze_currency(
            currency, start_date, end_date, return_period, factor_name)
        results[currency] = result

        # 提取关键指标到摘要数据
        summary_data.append({
            'currency': currency,
            'factor_name': factor_name,  # 添加因子名
            'start_date': start_date,
            'end_date': end_date,
            'return_period': return_period,
            'ic': result['ic'],
            'rank_ic': result['rank_ic'],
            'ir': result['ir'],
            'adf_p_value': result['adf_p_value'],
            'group_fig_path': result.get('group_fig_path', ''),
            'hist_fig_path': result.get('hist_fig_path', '')
        })

    # 创建摘要DataFrame
    summary_df = pd.DataFrame(summary_data)

    # 保存摘要DataFrame到CSV文件
    summary_path = 'results/multi_currency_summary.csv'
    summary_df.to_csv(summary_path, index=False)

    return results, summary_df


def analyze_different_time_periods(currency='ETHUSDT_15m_2020_2025', time_periods=None, return_period=96, factor_name='MA_Deviation'):
    """
    分析同一币种在不同时间段的因子表现

    参数:
        currency (str): 币种代码，格式如 'ETHUSDT_15m_2020_2025'
        time_periods (list): 时间段列表，每个元素为(开始日期, 结束日期)的元组
        return_period (int): 收益率计算周期，默认为96（对应15分钟K线的24小时）
        factor_name (str): 因子名称，默认为'MA_Deviation'（均线偏离因子）

    返回:
        dict: 包含所有时间段分析结果的字典
        pd.DataFrame: 包含关键指标的DataFrame
    """
    # 如果未提供时间段列表，使用默认列表
    if time_periods is None:
        time_periods = [
            ('2021-01-01', '2021-06-30'),  # 2021上半年
            ('2021-07-01', '2021-12-31'),  # 2021下半年
            ('2022-01-01', '2022-06-30')   # 2022上半年
        ]

    results = {}
    summary_data = []

    # 循环分析每个时间段
    for start_date, end_date in time_periods:
        period_name = f"{start_date} 至 {end_date}"

        # 分析币种在特定时间段
        result = analyze_currency(
            currency, start_date, end_date, return_period, factor_name)
        results[period_name] = result

        # 提取关键指标到摘要数据
        summary_data.append({
            'currency': currency,
            'factor_name': factor_name,  # 添加因子名
            'period': period_name,
            'start_date': start_date,
            'end_date': end_date,
            'return_period': return_period,
            'ic': result['ic'],
            'rank_ic': result['rank_ic'],
            'ir': result['ir'],
            'adf_p_value': result['adf_p_value'],
            'group_fig_path': result.get('group_fig_path', ''),
            'hist_fig_path': result.get('hist_fig_path', '')
        })

    # 创建摘要DataFrame
    summary_df = pd.DataFrame(summary_data)

    # 计算因子稳定性指标
    stability_metrics = {
        'ic_std': np.std(summary_df['ic']),
        'rank_ic_std': np.std(summary_df['rank_ic']),
        'ir_std': np.std(summary_df['ir'])
    }

    # 保存摘要DataFrame到CSV文件
    summary_path = f'results/{currency}_time_periods_summary.csv'
    summary_df.to_csv(summary_path, index=False)

    # 将稳定性指标添加到结果中
    results['stability_metrics'] = stability_metrics

    return results, summary_df


def compare_different_return_periods(currency='ETHUSDT_15m_2020_2025', start_date='2021-10-1', end_date=None, return_periods=None, factor_name='MA_Deviation'):
    """
    比较同一币种在不同收益率周期下的因子表现

    参数:
        currency (str): 币种代码，格式如 'ETHUSDT_15m_2020_2025'
        start_date (str): 起始日期，格式如 '2021-10-1'
        end_date (str): 结束日期，默认为None表示使用所有可用数据
        return_periods (list): 收益率周期列表，如果为None则使用默认列表
        factor_name (str): 因子名称，默认为'MA_Deviation'（均线偏离因子）

    返回:
        dict: 包含所有收益率周期分析结果的字典
        pd.DataFrame: 包含关键指标的DataFrame
    """
    # 如果未提供收益率周期列表，使用默认列表
    if return_periods is None:
        return_periods = [24, 48, 96, 192]  # 6小时, 12小时, 24小时, 48小时 (基于15分钟K线)

    results = {}
    summary_data = []

    # 循环分析每个收益率周期
    for period in return_periods:
        hours = period * 15 / 60
        period_name = f"{period}周期 ({hours}小时)"

        # 分析币种在特定收益率周期
        result = analyze_currency(
            currency, start_date, end_date, period, factor_name)
        results[period_name] = result

        # 提取关键指标到摘要数据
        summary_data.append({
            'currency': currency,
            'factor_name': factor_name,  # 添加因子名
            'start_date': start_date,
            'end_date': end_date,
            'return_period': period,
            'return_period_hours': hours,
            'period_name': period_name,
            'ic': result['ic'],
            'rank_ic': result['rank_ic'],
            'ir': result['ir'],
            'adf_p_value': result['adf_p_value'],
            'group_fig_path': result.get('group_fig_path', ''),
            'hist_fig_path': result.get('hist_fig_path', '')
        })

    # 创建摘要DataFrame
    summary_df = pd.DataFrame(summary_data)

    # 找出最佳收益率周期
    best_metrics = {
        'best_ic_period': summary_df.loc[summary_df['ic'].abs().idxmax(), 'period_name'],
        'best_ic_value': summary_df.loc[summary_df['ic'].abs().idxmax(), 'ic'],
        'best_rank_ic_period': summary_df.loc[summary_df['rank_ic'].abs().idxmax(), 'period_name'],
        'best_rank_ic_value': summary_df.loc[summary_df['rank_ic'].abs().idxmax(), 'rank_ic'],
        'best_ir_period': summary_df.loc[summary_df['ir'].abs().idxmax(), 'period_name'],
        'best_ir_value': summary_df.loc[summary_df['ir'].abs().idxmax(), 'ir']
    }

    # 保存摘要DataFrame到CSV文件
    summary_path = f'results/{currency}_return_periods_summary.csv'
    summary_df.to_csv(summary_path, index=False)

    # 将最佳指标添加到结果中
    results['best_metrics'] = best_metrics

    return results, summary_df


def run_single_currency_analysis(currency='ETHUSDT_15m_2020_2025', start_date='2021-10-1', end_date=None, return_period=96, factor_name='MA_Deviation'):
    """
    运行单一币种的完整分析

    参数:
        currency (str): 币种代码，格式如 'ETHUSDT_15m_2020_2025'
        start_date (str): 起始日期，格式如 '2021-10-1'
        end_date (str): 结束日期，默认为None表示使用所有可用数据
        return_period (int): 收益率计算周期，默认为96（对应15分钟K线的24小时）
        factor_name (str): 因子名称，默认为'MA_Deviation'（均线偏离因子）

    返回:
        dict: 包含分析结果的字典
    """
    # 分析单一币种
    result = analyze_currency(currency, start_date,
                              end_date, return_period, factor_name)

    # 保存结果到JSON文件
    import json

    # 创建可序列化的结果字典
    serializable_result = {
        'currency': currency,
        'factor_name': factor_name,  # 添加因子名字段
        'start_date': start_date,
        'end_date': end_date,
        'return_period': return_period,
        'adf_p_value': float(result['adf_p_value']),
        'ic': float(result['ic']),
        'rank_ic': float(result['rank_ic']),
        'ir': float(result['ir'])
    }

    # 保存结果
    # 文件名中也包含因子名
    result_path = f'results/{currency}_{factor_name}_analysis.json'
    with open(result_path, 'w') as f:
        json.dump(serializable_result, f, indent=4)

    return result


def run_multi_currency_analysis(currencies=None, start_date='2021-10-1', end_date=None, return_period=96, factor_name='MA_Deviation'):
    """
    运行多币种批量分析

    参数:
        currencies (list): 币种代码列表，如果为None则使用默认列表
        start_date (str): 起始日期，格式如 '2021-10-1'
        end_date (str): 结束日期，默认为None表示使用所有可用数据
        return_period (int): 收益率计算周期，默认为96（对应15分钟K线的24小时）
        factor_name (str): 因子名称，默认为'MA_Deviation'（均线偏离因子）

    返回:
        pd.DataFrame: 包含所有币种分析结果的DataFrame
    """
    # 运行多币种分析
    _, summary_df = analyze_multiple_currencies(
        currencies, start_date, end_date, return_period, factor_name)

    return summary_df


def run_time_period_analysis(currency='ETHUSDT_15m_2020_2025', time_periods=None, return_period=96, factor_name='MA_Deviation'):
    """
    运行不同时间段分析

    参数:
        currency (str): 币种代码，格式如 'ETHUSDT_15m_2020_2025'
        time_periods (list): 时间段列表，每个元素为(开始日期, 结束日期)的元组
        return_period (int): 收益率计算周期，默认为96（对应15分钟K线的24小时）
        factor_name (str): 因子名称，默认为'MA_Deviation'（均线偏离因子）

    返回:
        pd.DataFrame: 包含不同时间段分析结果的DataFrame
    """
    # 运行不同时间段分析
    _, summary_df = analyze_different_time_periods(
        currency, time_periods, return_period, factor_name)

    return summary_df


def run_return_period_analysis(currency='ETHUSDT_15m_2020_2025', start_date='2021-10-1', end_date=None, return_periods=None, factor_name='MA_Deviation'):
    """
    运行不同收益率周期分析

    参数:
        currency (str): 币种代码，格式如 'ETHUSDT_15m_2020_2025'
        start_date (str): 起始日期，格式如 '2021-10-1'
        end_date (str): 结束日期，默认为None表示使用所有可用数据
        return_periods (list): 收益率周期列表，如果为None则使用默认列表
        factor_name (str): 因子名称，默认为'MA_Deviation'（均线偏离因子）

    返回:
        pd.DataFrame: 包含不同收益率周期分析结果的DataFrame
    """
    # 运行不同收益率周期分析
    _, summary_df = compare_different_return_periods(
        currency, start_date, end_date, return_periods, factor_name)

    return summary_df


if __name__ == "__main__":
    """
    主函数，运行因子评估分析

    您可以通过修改analysis_type变量来选择要运行的分析类型:
    1: 单一币种分析 - 分析单个币种的因子表现
    2: 多币种分析 - 比较不同币种的因子表现
    3: 不同时间段分析 - 研究因子在不同市场环境下的表现
    4: 不同收益率周期分析 - 确定因子的最佳预测时间范围
    0: 运行所有分析
    """
    # 选择要运行的分析类型
    analysis_type = 1

    # 定义分析参数
    currency = 'ETHUSDT_15m_2020_2025'
    start_date = '2021-10-1'
    end_date = None
    return_period = 96
    factor_name = 'c_chu001'  # 因子名称

    # 多币种分析参数
    currencies = [
        'BTCUSDT_15m_2020_2025',
        'ETHUSDT_15m_2020_2025',
        'BNBUSDT_15m_2020_2025'
    ]

    # 不同时间段分析参数
    time_periods = [
        ('2021-01-01', '2021-06-30'),
        ('2021-07-01', '2021-12-31'),
        ('2022-01-01', '2022-06-30')
    ]

    # 不同收益率周期分析参数
    return_periods = [24, 48, 96, 192]

    # 运行选择的分析
    if analysis_type == 0 or analysis_type == 1:
        # 单一币种分析
        single_result = run_single_currency_analysis(
            currency=currency,
            start_date=start_date,
            end_date=end_date,
            return_period=return_period,
            factor_name=factor_name
        )

    if analysis_type == 0 or analysis_type == 2:
        # 多币种分析
        multi_currency_df = run_multi_currency_analysis(
            currencies=currencies,
            start_date=start_date,
            end_date=end_date,
            return_period=return_period,
            factor_name=factor_name
        )

    if analysis_type == 0 or analysis_type == 3:
        # 不同时间段分析
        time_period_df = run_time_period_analysis(
            currency=currency,
            time_periods=time_periods,
            return_period=return_period,
            factor_name=factor_name
        )

    if analysis_type == 0 or analysis_type == 4:
        # 不同收益率周期分析
        return_period_df = run_return_period_analysis(
            currency=currency,
            start_date=start_date,
            end_date=end_date,
            return_periods=return_periods,
            factor_name=factor_name
        )
