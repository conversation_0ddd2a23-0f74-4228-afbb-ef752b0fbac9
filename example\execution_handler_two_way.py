import math
from decimal import getcontext, Decimal


class ExecutionHandlerTwoWay:
    """
    交易执行引擎

    功能：
    - 根据交易信号执行买卖操作
    - 计算交易成本（滑点+手续费）
    - 管理持仓状态和可用资金

    核心参数说明：
    :param position_management_fn: 仓位管理策略函数
        - 输入：当前价格、信号强度、持仓量、可用资金、建议交易量
        - 输出：实际执行交易量（经过风控调整）
    :param config: 交易配置字典
        - initial_capital: 初始资金
        - slippage: 滑点比例（默认0.2%）
        - fee_rate: 手续费率（默认0.03%）
    """

    def __init__(self, position_management_fn, config):
        self.position_mgr = position_management_fn
        self.config = config
        self.trade_log = []
        self.open_position_records = []
        self.position = {
            'long_num': 0,
            'long_avg_cost': 0.0,
            'short_num': 0,
            'short_avg_cost': 0.0,
            'available_cash': config['initial_capital']
        }

    def execute_trade(self, signals, price, volume, date):
        # 仓位管理阶段：
        # 根据当前资金、持仓和风控规则，确定实际可交易量
        # 输出需要执行的交易信息数组
        trade_arr = self.position_mgr(
            signals=signals,
            price=price,
            position=self.position,
            open_position_records=self.open_position_records,
            date=date,
            config=self.config
        )
        if len(trade_arr) > 0:
            # 执行每笔交易
            for trade in trade_arr:
                self.execute_trade_detail(
                    signals, trade['signal'], trade['side'], trade['executed_price'], trade['trade_volume'], trade['fee'], trade['slippage'], date)

    """
        执行交易并记录逐笔委托与成交信息。
        :param signals: 因子信号
        :param signal: 交易信号
        :param side: 交易方向 SHORT LONG
        :param executed_price: 交易价格
        :param adjusted_volume: 交易数量
        :param fee: 手续费
        :param date: 当前日期
    """

    def execute_trade_detail(self, signals, signal, side, executed_price, adjusted_volume, fee, slippage, date):
        # 确保数值精度
        adjusted_volume = float(adjusted_volume)
        executed_price = float(executed_price)
        fee = float(fee)

        if adjusted_volume != 0:
            # 不包含手续费和滑点的交易价值
            trade_value = abs(adjusted_volume * executed_price)
            # 手续费和滑点的交易价值
            slippage_fee = trade_value * (fee + slippage)
            if trade_value + slippage_fee > self.position['available_cash']:
                print(f"[WARN] 资金不足，无法执行交易！")
                return
            # 买入操作处理流程：
            # 开多
            if signal == self.config['buy'] and side == 'LONG':
                # 保存原始持仓及持仓均价
                original_shares = self.position['long_num']
                original_avg_cost = self.position['long_avg_cost']

                # 更新持仓数量
                self.position['long_num'] += adjusted_volume

                # 计算交易成本（考虑方向）
                trade_value = executed_price * adjusted_volume

                # 同方向加仓：(原成本 * 原持仓绝对值 + 新增成本) / 新持仓绝对值
                self.position['long_avg_cost'] = (original_avg_cost * abs(original_shares) + trade_value) / abs(
                    self.position['long_num'])
                # 开多仓 可用资金 = 原可用资金 - 订单单价*订单数量 - 手续费 和 滑点费用
                self.position['available_cash'] = self.position['available_cash'] - \
                    trade_value - slippage_fee

                # 写入开仓记录
                self.open_position_records.append({
                    'date': date,
                    'side': 'LONG',
                    'avg_price': executed_price,
                    'volume': adjusted_volume
                })
            elif signal == self.config['sell'] and side == 'LONG':
                # 平多
                original_shares = self.position['long_num']
                original_price = self.position['long_avg_cost']
                close_volume = adjusted_volume

                # 现在的仓位数量 小于 交易的数量
                if round(original_shares, 4) < round(close_volume, 4):
                    close_volume = original_shares
                    print(
                        f"[WARN] {date} 当前仓位只有{original_shares}，平仓数量却是{adjusted_volume}！<平掉所有仓位>")
                self.position['long_num'] -= close_volume

                # 平仓获得资金：开仓均价 * 平仓数量 + （平仓价格 - 开仓价格）*平仓数量
                get_value = original_price * close_volume + \
                    (executed_price - original_price) * close_volume

                # 平多仓 可用资金 = 原可用资金 + 订单单价*订单数量 - 手续费 和 滑点费用
                self.position['available_cash'] = self.position['available_cash'] + \
                    get_value - slippage_fee
                if self.position['long_num'] == 0:
                    # 如果平仓至零，重置均价
                    self.position['long_avg_cost'] = 0
            # 卖出操作处理流程：
            # 开空仓
            elif signal == self.config['sell'] and side == 'SHORT':
                # 保存原始持仓 及 持仓价格
                original_shares = self.position['short_num']
                original_avg_cost = self.position['short_avg_cost']

                # 更新持仓数量
                self.position['short_num'] -= adjusted_volume

                # 计算交易价值和到账金额
                trade_value = executed_price * adjusted_volume

                # 同方向加仓（卖出增加空头）：(原成本 * 原持仓绝对值 + 新增成本) / 新持仓绝对值
                self.position['short_avg_cost'] = (original_avg_cost * abs(original_shares) + trade_value) / abs(
                    self.position['short_num'])

                # 开空仓 可用资金 = 原可用资金 - 订单单价*订单数量 - 手续费 和滑点费用
                self.position['available_cash'] = self.position['available_cash'] - \
                    trade_value - slippage_fee
                # 写入开仓记录
                self.open_position_records.append({
                    'date': date,
                    'side': 'SHORT',
                    'avg_price': executed_price,
                    'volume': adjusted_volume
                })
            elif signal == self.config['buy'] and side == 'SHORT':
                # 平空仓
                original_shares = self.position['short_num']
                original_price = self.position['short_avg_cost']
                close_volume = adjusted_volume

                # 现在的仓位数量 小于 交易的数量
                if round(abs(original_shares), 4) < round(close_volume, 4):
                    close_volume = abs(original_shares)
                    print(
                        f"[WARN] {date} 当前仓位只有{original_shares}，平仓数量却是{adjusted_volume}！<平掉所有仓位>")
                self.position['short_num'] += close_volume

                # 平仓获得资金：开仓均价 * 平仓数量 + （开仓价格 - 平仓价格）*平仓数量
                get_value = original_price * close_volume + \
                    (original_price - executed_price) * close_volume

                # 平空仓 可用资金 = 原可用资金 + 平仓获得资金 - 手续费 和 滑点费用
                self.position['available_cash'] = self.position['available_cash'] + \
                    get_value - slippage_fee
                if self.position['short_num'] == 0:
                    # 如果平仓至零，重置均价
                    self.position['short_avg_cost'] = 0
            else:
                print(
                    f"[ERROR] {date} 交易失败，信号、方向未匹配上，因子信号: {signals}, 交易信号: {signal}, 交易side: {side}")

            # 净值(可用余额 + 空仓成本价值 + 多仓成本价值)
            value = round(self.position['available_cash'] + self.position['short_avg_cost'] * abs(self.position['short_num'])
                          + self.position['long_avg_cost'] * self.position['long_num'], 2)

            # 记录交易日志
            self.trade_log.append({
                'date': date,
                'signal': signals,
                'fn_signal': signal,
                'side': side,
                'price': executed_price,
                'volume': adjusted_volume,
                'transaction_cost': slippage_fee,
                'long_position': round(self.position['long_num'], 2),
                'long_avg_price': round(self.position['long_avg_cost'], 2),
                'short_position': round(self.position['short_num'], 2),
                'short_avg_price': round(self.position['short_avg_cost'], 2),
                'cash': round(self.position['available_cash'], 2),
                'value': value
            })

    """
    获取逐笔交易记录
    :return: 逐笔交易记录
    """

    def get_trade_log(self):
        return self.trade_log

    """
    打印逐笔交易记录
    """

    def print_trade_log(self):
        for log in self.trade_log:
            print(f"{log['date']} | {'买入' if log['signal'] == 1 else '卖出'} "
                  f"{log['volume']}股 @ {log['price']:.2f}  "
                  f"手续费:{log['fee']:.2f} 持仓:{log['position']} 现金:{log['cash']:.2f}")
