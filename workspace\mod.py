from sqlalchemy import create_engine
from pandas.errors import PerformanceWarning
import pandas as pd
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt
import scipy.stats as stats
import warnings
warnings.filterwarnings(
    "ignore", message=".*swapaxes.*", category=FutureWarning)

warnings.simplefilter("ignore", PerformanceWarning)

MYSQL_CONFIG = {
    "host": "************",
    "user": "root",
    "password": "1234",
    "database": "ctadata"
}


class DataManager():

    def __init__(self):
        # self.__tim__ = ticker.TickerInfoManager()

        self.problem_ticker = []
        self.engine = create_engine(f"mysql+pymysql://{MYSQL_CONFIG['user']}:{
                                    MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}/{MYSQL_CONFIG['database']}")
        return

    def concat_data(self, data):
        """读取并合并单品类永续合约的K线数据"""
        query = f"SELECT * FROM {data.contract}"
        df = pd.read_sql(query, self.engine)
        df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
        df.set_index('open_time', inplace=True)
        # 将所有K线数据合并
        data.data = df
        return


class Data():

    def __init__(self, contract):
        self.contract = contract
        return


def analyze_extreme_values(df, col):
    """头尾部超额分析"""
    q10, q90 = np.percentile(df[col].dropna(), [10, 90])  # 计算 10% 和 90% 分位数

    # 取头部和尾部数据
    tail_data = df[df[col] <= q10]
    head_data = df[df[col] >= q90]

    # 计算统计描述
    tail_stats = tail_data.describe()
    head_stats = head_data.describe()

    return tail_stats, head_stats


if __name__ == '__main__':
    currency = 'BTCUSDT_15m_2022_2025'
    d = Data(currency)
    dm = DataManager()
    dm.concat_data(d)

    for t in range(10, 11):

        # 向后移动t个周期的收益率,初始t=10
        df = d.data.copy()
        df['close_shifted'] = df['close'].shift(-t)
        df['return'] = (df['close_shifted'] - df['close']) / df['close']

        #############################################
        # 计算真实波动幅度（tr）
        df['tr1'] = df['high'] - df['low']
        df['tr2'] = abs(df['high'] - df['close'].shift(1))  # 参考前一周期的收盘价
        df['tr3'] = abs(df['close'].shift(1) - df['low'])
        df['tr'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)

        window = 14

        # 计算 atr
        df['atr'] = df['tr'].ewm(alpha=1/window, adjust=False).mean()

        q1 = df['atr'].quantile(0.33)  # 33% 分位数
        q2 = df['atr'].quantile(0.66)  # 66% 分位数

        #############################################
        # 计算alpha_046因子
        df['alpha_046'] = (df['close'].rolling(3).mean() +
                           df['close'].rolling(6).mean() +
                           df['close'].rolling(12).mean() +
                           df['close'].rolling(24).mean()) / (4 * df['close'])

        #############################################
        # 计算ats因子
        df['ats'] = df['volume']/df['trade_count']

        #############################################

        # 去除nan
        df = df.dropna()
        # 掐头去尾（有些因子刚开始的时候计算不太准，要依赖长时间的数据）
        df = df.iloc[100:-100]
        # 计算 pearson 相关系数
        pearson_corr_matrix = df[['atr', 'alpha_046',
                                  'ats', 'return']].corr(method='pearson')

        '''
        第一部分，统计显著性检验
        
        '''
        # 选择要检验的因子
        tested_factor = 'alpha_046'

        print(df[tested_factor].describe())
        plt.figure()
        sns.histplot(df[tested_factor], bins=30,
                     kde=True)  # kde=True 显示核密度估计曲线
        plt.title(f'{tested_factor} sistribution')
        plt.show(block=False)

        r = df[tested_factor].corr(df['return'])  # 计算相关性
        n = len(df)  # 样本量

        # 计算t值
        t_stat = r * ((n - 2) ** 0.5) / ((1 - r**2) ** 0.5)

        # 计算p值
        p_value = 2 * (1 - stats.t.cdf(abs(t_stat), df=n-2))
        print('='*50)
        print('-'*20, f'{tested_factor}', '-'*20)
        print(f'----------{currency}----------')
        print(f'----------相关系数: {r:.6f}----------')
        print(f'----------t值: {t_stat:.2f}----------p值: {p_value:.6f}')
        print('='*50)

        '''
        第二部分，交叉验证
        '''
        # 直接按行索引均分为 10 份
        chunks = np.array_split(df[[tested_factor, 'return']], 10)

        # 计算每份的 pearson 相关系数
        pearson_corrs = [chunk[[tested_factor, 'return']].corr().iloc[0, 1]
                         for chunk in chunks]

        # 输出相关系数列表
        print('每组数据的相关系数:')
        for corr in pearson_corrs:
            print(corr)
        print('='*50)
        print('IR值:', np.mean(pearson_corrs)/np.std(pearson_corrs))

        # 头尾部超额
        tail_stats, head_stats = analyze_extreme_values(
            df, col=tested_factor)

        print('='*50)
        print("尾部return数据统计:")
        # print(tail_stats['return'])
        print(f'简单夏普: {tail_stats["return"]
              ["mean"]/tail_stats["return"]["std"]}')
        print(tail_stats['return']
              ['count']*tail_stats['return']['mean'])

        print('='*50)
        print("头部return数据统计:")
        # print(head_stats['return'])
        print(f'简单夏普: {head_stats["return"]
              ["mean"]/head_stats["return"]["std"]}')
        print(head_stats['return']
              ['count']*head_stats['return']['mean'])

        print('='*50)

        # 画折线图
        plt.figure(figsize=(8, 5))
        plt.plot(range(1, 11), pearson_corrs, marker='o',
                 linestyle='-', color='b', label='Pearson Correlation')

        # 添加标题和坐标轴标签
        plt.title(f'{currency} pearson Correlation')
        plt.xlabel('Chunk Index')
        plt.ylabel('Correlation Coefficient')
        plt.xticks(range(1, 11))  # 设置 x 轴刻度
        plt.axhline(y=0, color='gray', linestyle='--', linewidth=1)  # 添加零轴参考线
        plt.legend()
        plt.grid(True)

        # 显示图表
        plt.show(block=False)

        # 头尾部超额

        '''
        第三部分，经济学检验,见mark
        '''

        '''
        第四部分，可行性检验(待完成)
        '''
        # #############################################
        # 画pearson 相关系数热力图
        plt.figure(figsize=(14, 12))
        sns.heatmap(pearson_corr_matrix, annot=True, annot_kws={'fontsize': 10},
                    cmap='coolwarm', fmt=".5f", linewidths=0.5)
        plt.title("Heatmap of Pearson Correlation Matrix",
                  fontsize=20, fontweight='bold')
        plt.xticks(fontsize=14, rotation=45)
        plt.yticks(fontsize=14, rotation=0)
        plt.show()

        # 绘制散点图(这里面的0.0025，0.005要根据具体的因子值来改变)
        # plt.figure(figsize=(10, 6))
        # plt.scatter(df[df[tested_factor] < 0.0125][tested_factor], df[df[tested_factor] <
        #             0.0125]['return'], s=1, color='blue', alpha=0.6,)
        # print(np.corrcoef(df[tested_factor],
        #       df['return']))
        # print(np.corrcoef(df[df[tested_factor] < 0.0125][tested_factor],
        #       df[df[tested_factor] < 0.0125]['return']))
        # plt.xlabel(tested_factor)
        # plt.ylabel('Return')
        # plt.title('Scatter Plot')
        # plt.grid(True)
        # plt.show()
        # print(df[(df[tested_factor] > 0) & (
        #     df[tested_factor] < 0.0025)]['return'].describe())
        # print(df[(df[tested_factor] > 0.0025) & (
        #     df[tested_factor] < 0.005)]['return'].describe())
        # print(df[(df[tested_factor] > 0.005) & (
        #     df[tested_factor] < 0.0075)]['return'].describe())
        # print(df[(df[tested_factor] > 0.0075) & (
        #     df[tested_factor] < 0.01)]['return'].describe())
        # print(df[(df[tested_factor] > 0.01) & (
        #     df[tested_factor] < 0.0125)]['return'].describe())
        # print(df[(df[tested_factor] > 0.0125) & (
        #     df[tested_factor] < 0.015)]['return'].describe())
