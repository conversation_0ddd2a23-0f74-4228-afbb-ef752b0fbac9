import numpy as np


class StatisticsHandler:
    def __init__(self):
        self.results = {}

    # 计算总收益率
    def calculate_total_profit(self, equity_curve):
        if equity_curve.iloc[0] == 0:
            raise ValueError("初始资金不能为空！")
        return (equity_curve.iloc[-1] - equity_curve.iloc[0]) / equity_curve.iloc[0] * 100

    # 计算最大收益率
    def calculate_maximum_profit(self, equity_curve):
        if len(equity_curve) == 0:
            raise ValueError("账户净值曲线不能为空！")

        max_profit = 0
        low = equity_curve.iloc[0]

        for i in range(1, len(equity_curve)):
            current_value = equity_curve.iloc[i]
            if current_value < low:
                low = current_value
            else:
                current_profit = (current_value - low) / low
                max_profit = max(max_profit, current_profit)

        return max_profit * 100

    # 计算最大回撤
    def calculate_maximum_drawdown(self, equity_curve):
        if len(equity_curve) == 0:
            raise ValueError("账户净值曲线不能为空！")
        if len(equity_curve) <= 1:
            return 0  # 如果只有0或1个点，没有回撤

        # 初始化最大回撤和当前最高点
        max_drawdown = 0
        peak = equity_curve.iloc[0]  # 从第一个点开始

        # 遍历累计收益，找到最大回撤
        for i in range(1, len(equity_curve)):
            current_value = equity_curve.iloc[i]
            # 更新当前最高点
            if current_value > peak:
                peak = current_value
            elif peak > 0:
                # 计算当前回撤
                drawdown = (peak - current_value) / peak * 100
                # 更新最大回撤
                if drawdown > max_drawdown:
                    max_drawdown = drawdown

        return max_drawdown

    # 计算简单夏普
    def calculate_sharpe_ratio(self, equity_curve):
        if len(equity_curve) == 0:
            raise ValueError("账户净值曲线不能为空！")

        profit = equity_curve.pct_change().dropna()
        profit_len = profit[profit != 0]
        if len(profit_len) == 0:
            return 0
        return profit_len.mean() / profit_len.std()

    # 计算盈亏比
    def calculate_loss_ratio(self, equity_curve):
        if len(equity_curve) == 0:
            raise ValueError("账户净值曲线不能为空！")

        profit_and_loss = equity_curve.diff()
        profit = profit_and_loss[profit_and_loss > 0]
        loss = profit_and_loss[profit_and_loss < 0]

        if len(profit) == 0 or len(loss) == 0:
            return 0
        return profit.mean() / abs(loss.mean())

    # 计算胜率
    def calculate_win_rate(self, equity_curve):
        if len(equity_curve) == 0:
            raise ValueError("账户净值曲线不能为空！")

        profit_and_loss = equity_curve.diff()
        profit_and_loss = profit_and_loss[profit_and_loss != 0]
        profit = profit_and_loss[profit_and_loss > 0]
        if len(profit_and_loss) == 0:
            return 0
        return len(profit) / len(profit_and_loss) * 100

    def calculate(self, equity_curve):
        """
        计算并存储回测的统计指标(最大收益,最大回撤,夏普比,盈亏比,胜率等效益指标)
        :param equity_curve: 账户净值曲线
        :param daily_returns: 每日收益
        :param transactions: 逐笔交易记录
        :param config: 配置文件
        """

        # 总收益
        self.results["total_profit"] = self.calculate_total_profit(
            equity_curve)
        # 最大收益
        self.results["maximum_profit"] = self.calculate_maximum_profit(
            equity_curve)
        # 最大回撤
        self.results["maximum_drawdown"] = self.calculate_maximum_drawdown(
            equity_curve)
        # 夏普比
        self.results["sharpe_ratio"] = self.calculate_sharpe_ratio(
            equity_curve)
        # 盈亏比
        self.results["loss_ratio"] = self.calculate_loss_ratio(equity_curve)
        # 胜率
        self.results["win_rate"] = self.calculate_win_rate(equity_curve)

    def print_results(self):
        """
        打印统计指标
        """
        print(f"总收益：{self.results['total_profit']:.2f}%\n最大收益：{self.results['maximum_profit']:.2f}%\n最大回撤：{self.results['maximum_drawdown']:.2f}%\n夏普比：{self.results['sharpe_ratio']}\n盈亏比：{self.results['loss_ratio']}\n胜率：{self.results['win_rate']:.2f}%")

        pass
