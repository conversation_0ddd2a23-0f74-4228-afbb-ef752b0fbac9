import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy import stats
from statsmodels.tsa.stattools import adfuller


class TimeSeriesHomogeneityTest:
    def __init__(self, time_series, debug=False):
        """
        初始化类，传入时间序列数据。

        :param time_series: 时间序列数据，可以是list、numpy数组或pandas Series。
        """
        self.time_series = pd.Series(time_series)
        self.debug = debug

    def plot_time_series(self, window_size=None, max_windows=100, bins=200):
        """
        绘制时间序列的直方图，或者在不同时间窗口绘制直方图以便用户主观判断。

        :param window_size: 如果提供，将时间序列分成多个窗口并分别绘制。
        :param max_windows: 最大窗口数量，避免绘制过多子图。
        :param bins: 直方图的柱数。
        """
        if window_size:
            num_windows = min(len(self.time_series) //
                              window_size, max_windows)
            fig, axes = plt.subplots(
                num_windows, 1, figsize=(10, num_windows * 3))
            for i in range(num_windows):
                start = i * window_size
                end = start + window_size
                window_data = self.time_series[start:end]
                axes[i].hist(window_data, bins=bins, color='blue', alpha=0.7)
                axes[i].set_title(f'Window {i+1}')
            plt.tight_layout()
        else:
            plt.figure(figsize=(10, 6))
            plt.hist(self.time_series, bins=bins, color='blue', alpha=0.7)
            plt.title('Histogram of Time Series')
        plt.show()

    def compare_statistics(self, window_size, statistic='mean'):
        """
        比较不同时间窗口的统计量（如均值、方差等）是否一致。

        :param window_size: 时间窗口的大小。
        :param statistic: 要比较的统计量，可以是 'mean', 'var', 'skew', 'kurtosis' 等。
        :return: 各窗口的统计量。
        """
        num_windows = len(self.time_series) // window_size
        statistics = []
        for i in range(num_windows):
            start = i * window_size
            end = start + window_size
            window_data = self.time_series[start:end]
            if statistic == 'mean':
                statistics.append(window_data.mean())
            elif statistic == 'var':
                statistics.append(window_data.var())
            elif statistic == 'skew':
                statistics.append(window_data.skew())
            elif statistic == 'kurtosis':
                statistics.append(window_data.kurtosis())
            else:
                raise ValueError(f"Unsupported statistic: {statistic}")
        return statistics

    def hypothesis_test(self, window_size, test='ks'):
        """
        使用假设检验方法检验不同时间窗口的分布是否一致。

        :param window_size: 时间窗口的大小。
        :param test: 假设检验方法，可以是 'ks' (Kolmogorov-Smirnov) 或 'adf' (Augmented Dickey-Fuller)。
        :return: 检验结果的列表，每个元素包含窗口索引、检验方法、p 值、原假设和结论。
        """
        num_windows = len(self.time_series) // window_size
        test_results = []  # 用于保存每个窗口的检验结果

        for i in range(1, num_windows):
            window1 = self.time_series[(i-1) * window_size: i * window_size]
            window2 = self.time_series[i * window_size: (i + 1) * window_size]

            if test == 'ks':
                # Kolmogorov-Smirnov 检验：检验两个样本是否来自同一分布
                _, p_value = stats.ks_2samp(window1, window2)
                h0 = "H0: The distributions of the two samples are the same."
                conclusion = "Fail to reject H0 (Distributions are the same)" if p_value > 0.05 else "Reject H0 (Distributions are different)"
            elif test == 'adf':
                # Augmented Dickey-Fuller 检验：检验时间序列是否平稳
                adf_result = adfuller(window1 - window2)
                p_value = adf_result[1]
                h0 = "H0: The time series is non-stationary."
                conclusion = "Fail to reject H0 (Series is non-stationary)" if p_value > 0.05 else "Reject H0 (Series is stationary)"
            else:
                raise ValueError(f"Unsupported test: {test}")

            # 保存检验结果
            test_results.append({
                "Window Pair": f"{i} vs {i+1}",
                "Test": test,
                "H0": h0,
                "p-value": p_value,
                "Conclusion": conclusion
            })

        # 格式化输出检验结果
        if self.debug:
            for result in test_results:
                print(f"Window Pair: {result['Window Pair']}")
                print(f"Test: {result['Test']}")
                print(f"H0: {result['H0']}")
                print(f"p-value: {result['p-value']:.4f}")
                print(f"Conclusion: {result['Conclusion']}\n")

        return test_results

    def rolling_statistics(self, window_size, statistic='mean'):
        """
        计算滚动统计量，用于观察时间序列的稳定性。

        :param window_size: 滚动窗口的大小。
        :param statistic: 要计算的统计量，可以是 'mean', 'var', 'skew', 'kurtosis' 等。
        :return: 滚动统计量。
        """
        if statistic == 'mean':
            rolling_stat = self.time_series.rolling(window=window_size).mean()
        elif statistic == 'var':
            rolling_stat = self.time_series.rolling(window=window_size).var()
        elif statistic == 'skew':
            rolling_stat = self.time_series.rolling(window=window_size).skew()
        elif statistic == 'kurtosis':
            rolling_stat = self.time_series.rolling(window=window_size).kurt()
        else:
            raise ValueError(f"Unsupported statistic: {statistic}")
        return rolling_stat

    def plot_rolling_statistics(self, window_size, statistic='mean'):
        """
        绘制滚动统计量图，并添加均值线以便比较。

        :param window_size: 滚动窗口的大小。
        :param statistic: 要计算的统计量，可以是 'mean', 'var', 'skew', 'kurtosis' 等。
        """
        rolling_stat = self.rolling_statistics(window_size, statistic)
        plt.figure(figsize=(10, 6))
        plt.plot(rolling_stat, label=f'Rolling {statistic}')
        plt.axhline(rolling_stat.mean(), color='red',
                    linestyle='--', label=f'Mean {statistic}')
        plt.title(
            f'Rolling {statistic.capitalize()} with Window Size {window_size}')
        plt.legend()
        plt.show()


# all_fields = d_fields+c_fields+ori_fields+kline_fields
# # 示例用法
# all_df,_,_ = get_data("BTCUSDT_15m_2020_2025",all_fields,96)
# N = 96*7
# homofactor = []
# heterfactor = []
# for factor in  all_fields:

# factor = """ """
# ts_test = TimeSeriesHomogeneityTest(all_df[factor])
# # ts_test.plot_rolling_statistics(window_size=3*N)
# p_mean = pd.DataFrame(ts_test.hypothesis_test(window_size=N))['p-value'].mean()
# if p_mean <= 0.05:
#     heterfactor.append(factor)
# else:
#     homofactor.append(factor)
