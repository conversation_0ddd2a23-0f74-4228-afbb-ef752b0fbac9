"""
运行多仓位信号策略回测
使用df_temp.pkl数据,根据signals列的值进行交易
"""

import pandas as pd
import numpy as np
from datetime import datetime
import traceback

from vnpy_backtester.engines.engine import BacktestingEngine
from vnpy_backtester.objects.object import BarData
from vnpy_backtester.utils.constant import Exchange
from vnpy_backtester.strategies.multi_position_strategy import MultiPositionStrategy
from vnpy_backtester.utils.chart_engine import PlotlyChartEngine


def run_multi_position_strategy_backtest(df=None, data_file=None, holding_bars=96, position_size=1, rate=0.0003, slippage=0.01, capital=50000, plot_show=True, plot_save=True, order_type="quantity", max_positions=None, **strategy_params):
    """
    运行多仓位信号策略回测

    参数:
    df: 数据DataFrame,如果为None则从data_file加载
    data_file: 数据文件路径,仅在df为None时使用
    holding_bars: 持仓K线数量,默认为96
    position_size: 每次开仓的数量或金额,默认为1
                  当order_type="quantity"时表示币的数量
                  当order_type="amount"时表示开仓金额
    rate: 手续费率,默认为0.0003
    slippage: 滑点,默认为0.01
    capital: 初始资金,默认为50000
    plot_show: 是否在浏览器中显示图表,默认为True
    plot_save: 是否保存图表文件,默认为True
    order_type: 下单方式,可选"quantity"(按币数量)或"amount"(按金额),默认为"quantity"
    max_positions: 最大持仓数限制,默认为None(无限制),可设置为10表示最多持有10个仓位
    **strategy_params: 其他策略参数

    DataFrame要求:
    传入的DataFrame必须包含以下列:
    - open: float类型,开盘价
    - high: float类型,最高价
    - low: float类型,最低价
    - close: float类型,收盘价
    - volume: float类型,成交量
    - signals: int或float类型,交易信号,1表示做多,-1表示做空,0表示不操作

    DataFrame索引要求:
    - 索引应为datetime类型,表示每个K线的时间
    - 如果索引不是datetime类型,函数会尝试使用'datetime'列作为索引
    - 如果没有'datetime'列,会创建一个简单的日期索引

    示例DataFrame格式:

                        open    high    low     close   volume  signals
    2021-01-01 00:00:00 100.0   101.0   99.0    100.5   1000.0  0
    2021-01-01 00:15:00 100.5   102.0   100.0   101.5   1200.0  1
    2021-01-01 00:30:00 101.5   101.8   100.8   101.0   800.0   -1
    ...
    """
    # 加载数据
    print("加载数据文件...")

    print(f"成功加载数据,共 {len(df)} 条记录")
    # 不输出数据示例,减少终端输出
    # print("数据示例:")
    # print(df.head())

    # 检查必要的列是否存在
    required_columns = ["open", "high", "low", "close", "volume", "signals"]
    missing_columns = [
        col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"错误: 数据中缺少必要的列: {missing_columns}")
        return

    # 检查数据类型
    try:
        # 检查价格和成交量列是否可以转换为float类型
        for col in ["open", "high", "low", "close", "volume"]:
            df[col] = df[col].astype(float)

        # 检查signals列是否可以转换为float类型
        df["signals"] = df["signals"].astype(float)
    except Exception as e:
        print(f"错误: 数据类型转换失败: {e}")
        return

    # 检查索引是否为datetime类型
    if not isinstance(df.index, pd.DatetimeIndex):
        print("警告: 索引不是datetime类型,尝试使用'datetime'列作为索引")
        if "datetime" in df.columns:
            try:
                df.index = pd.to_datetime(df["datetime"])
                print("成功使用'datetime'列作为索引")
            except Exception as e:
                print(f"错误: 无法将'datetime'列转换为日期索引: {e}")
                print("创建一个简单的日期索引")
                start_date = datetime(2021, 1, 1)
                df.index = pd.date_range(
                    start=start_date, periods=len(df), freq="15min")
        else:
            print("警告: 没有'datetime'列,创建一个简单的日期索引")
            start_date = datetime(2021, 1, 1)
            df.index = pd.date_range(
                start=start_date, periods=len(df), freq="15min")

    # 将最后holding_bars根K线的signals设置为0,避免在回测结束前开仓
    if len(df) > holding_bars:
        # 获取最后holding_bars根K线的索引
        last_bars_indices = df.index[-holding_bars:]
        # 设置信号为0
        df.loc[last_bars_indices, 'signals'] = 0
        # 简化输出信息
        print(f"已将最后{holding_bars}根K线的信号设置为0")

    # 创建Bar对象列表 - 使用向量化操作提高效率
    print("准备回测数据...")

    # 预先创建所有需要的对象,避免在循环中创建
    bars = []

    # 使用pandas的itertuples方法,比iterrows更高效
    for row in df.itertuples():
        # 获取行的索引(日期时间)
        dt = row.Index

        # 创建BarData对象
        bar = BarData(
            symbol="ETHUSDT",
            exchange=Exchange.BINANCE,
            datetime=dt,  # 使用原始的datetime对象
            open_price=row.open,
            high_price=row.high,
            low_price=row.low,
            close_price=row.close,
            volume=row.volume,
            # 如果没有turnover列,使用volume*close
            turnover=getattr(row, "turnover", row.volume * row.close),
            gateway_name="BACKTEST"
        )
        # 添加signals属性
        setattr(bar, "signals", row.signals)
        bars.append(bar)

    # 设置回测引擎
    position_desc = f"每次开仓数量: {position_size}" if order_type == "quantity" else f"每次开仓金额: {position_size}"
    print(
        f"设置回测引擎... (持仓K线数量: {holding_bars}, {position_desc}, 下单方式: {order_type})")
    engine = BacktestingEngine()

    # 合并所有参数
    params = {
        "vt_symbol": "ETHUSDT.BINANCE",
        "start": df.index[0],
        "end": df.index[-1],
        "rate": rate,  # 手续费率
        "slippage": slippage,  # 滑点
        "capital": capital,  # 初始资金
        "holding_bars": holding_bars,  # 持仓K线数量
        "position_size": position_size,  # 每次开仓的数量或金额
        "order_type": order_type,  # 下单方式：按币数量或按金额
        "max_positions": max_positions  # 最大持仓数限制
    }

    # 添加其他策略参数
    if strategy_params:
        params.update(strategy_params)

    # 设置参数
    engine.set_parameters(**params)

    # 添加策略
    print("添加策略...")
    engine.add_strategy(MultiPositionStrategy)

    # 加载数据
    engine.history_data = bars

    # 运行回测
    print("开始回测...")
    engine.run_backtesting()

    # 计算结果
    engine.calculate_result()

    # 显示统计指标
    stats = engine.calculate_statistics()
    print("\n====== 回测结果 ======")
    print(f"策略: MultiPositionStrategy")
    print(f"开始日期: {stats['start_date']}  结束日期: {stats['end_date']}")
    print(
        f"总交易日: {stats['total_days']}  盈利日: {stats['profit_days']}  亏损日: {stats['loss_days']}")
    print(f"初始资金: {stats['capital']:,.2f}  结束资金: {stats['end_balance']:,.2f}")
    print(f"总收益率: {stats['total_return']:,.2f}%")
    print(
        f"最大回撤: {stats['max_drawdown']:,.2f}%  夏普比率: {stats['sharpe_ratio']:,.2f}")

    # 输出交易统计(直接拼接到回测结果后面)
    if 'total_trades' in stats:
        print(
            f"总交易次数: {stats['total_trades']}  盈利交易: {stats['winning_trades']}  亏损交易: {stats['losing_trades']}")
        # 使用profit_ratio作为盈亏比,profit_factor作为利润因子
        if 'profit_ratio' in stats:
            print(
                f"胜率: {stats['win_rate']:,.2f}%  盈亏比: {stats['profit_ratio']:,.2f}")
        else:
            print(
                f"胜率: {stats['win_rate']:,.2f}%  利润因子: {stats['profit_factor']:,.2f}")
        if 'avg_winning' in stats and 'avg_losing' in stats:
            print(
                f"平均盈利: {stats['avg_winning']:,.2f}  平均亏损: {stats['avg_losing']:,.2f}")

        # 只显示总盈亏
        balance_pnl = stats['end_balance'] - stats['capital']
        print(f"总盈亏: {balance_pnl:,.2f}")

    # 使用PlotlyChartEngine显示资金曲线
    if plot_show or plot_save:  # 只有在需要显示或保存图表时才创建图表
        try:
            # 创建图表引擎
            chart_engine = PlotlyChartEngine()

            # 获取初始资金
            initial_capital = stats['capital']

            # 创建图表文件路径
            order_type_str = "quantity" if order_type == "quantity" else "amount"
            chart_file_path = f"vnpy_backtester/charts/multi_position_strategy_result_{holding_bars}bars_{order_type_str}.html"

            # 根据参数决定是否显示和保存图表
            save_path = chart_file_path if plot_save else None

            # 创建图表标题
            position_desc = f"每次开仓数量: {position_size}" if order_type == "quantity" else f"每次开仓金额: {position_size}"
            title = f"多仓位信号策略回测结果 (持仓K线数量: {holding_bars}, {position_desc})"

            # 创建图表
            chart_engine.create_chart(
                engine=engine,
                title=title,
                save_path=save_path,
                show=plot_show,
                initial_capital=initial_capital
            )

            # 如果保存了图表,显示保存信息
            if plot_save:
                print(f"图表已保存为 {chart_file_path}")

            # 显示图表状态信息
            if not plot_show and not plot_save:
                print("图表未显示和保存 (plot_show=False, plot_save=False)")
            elif not plot_show:
                print("图表未在浏览器中显示 (plot_show=False)")
            elif not plot_save:
                print("图表未保存 (plot_save=False)")

        except Exception as e:
            print(f"图表处理时出错: {e}")
            traceback.print_exc()
            if plot_show:
                try:
                    # 如果Plotly出错,尝试使用原始的show_chart方法
                    engine.show_chart()
                except Exception as e2:
                    print(f"原始图表显示也出错: {e2}")
    else:
        print("图表未显示和保存 (plot_show=False, plot_save=False)")

    return engine


if __name__ == "__main__":

    # 传入数据
    # df = pd.read_csv(
    #     r'C:\Users\<USER>\python sc\example\回测结果250411_1.csv')  # 15m

    df = pd.read_pickle(
        r'C:\Users\<USER>\Desktop\kline_data_2025-05-30_14儶57儶15.pkl')
    df['signals'] = df['signal']
    df = df['2023-07-1':'2023-07-15']

    # df = pd.read_excel(
    #     r'C:\Users\<USER>\Desktop\20250522BTC_TEST01.xlsx')

    # df['signals'] = df['signal']

    # df = pd.read_csv(
    #     r'C:\Users\<USER>\python sc\example\250416test02.csv')  # 1h

    # df = pd.read_pickle(
    #     r'C:\Users\<USER>\python sc\vnpy_backtester\data\df_example.pkl')  # 测试数据

    # 设置index
    # df = df.set_index('Unnamed: 0').rename_axis('open_time', axis=0)
    df.index = pd.to_datetime(df.index)  # index必须是datetime类型

    # 运行回测程序
    engine = run_multi_position_strategy_backtest(
        df=df,
        holding_bars=10,
        position_size=1000,       # 数量或金额
        rate=0.0003,
        slippage=0.000,
        capital=15000,
        order_type="amount",  # 可选 "quantity"(按币数量) 或 "amount"(按金额)
        max_positions=10,  # 最大持仓数限制，None表示无限制
    )

    # 示例：如何使用自定义DataFrame进行回测
    """
    # 创建或加载自定义DataFrame
    custom_df = pd.read_csv('your_data.csv')

    # 确保DataFrame包含必要的列:open, high, low, close, volume, signals
    # 如果使用日期索引,确保索引是datetime类型
    custom_df.index = pd.to_datetime(custom_df['datetime'])

    # 按币数量下单示例
    engine1 = run_multi_position_strategy_backtest(
        df=custom_df,
        position_size=1,  # 每次开仓1个币
        order_type="quantity",  # 按币数量下单
        max_positions=5  # 最多持有5个仓位
    )

    # 按金额下单示例
    engine2 = run_multi_position_strategy_backtest(
        df=custom_df,
        position_size=1000,  # 每次开仓1000元
        order_type="amount",  # 按金额下单
        max_positions=10  # 最多持有10个仓位
    )
    """
