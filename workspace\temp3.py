# In[1]
from statsmodels.tsa.stattools import adfuller
from scipy import stats
from sqlalchemy import create_engine
import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt

MYSQL_CONFIG = {
    "host": "************",
    "user": "root",
    "password": "1234",
    "database": "ctadata"
}


class DataManager():

    def __init__(self):
        # self.__tim__ = ticker.TickerInfoManager()
        self.problem_ticker = []
        self.engine = create_engine(f"mysql+pymysql://{MYSQL_CONFIG['user']}:{
                                    MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}/{MYSQL_CONFIG['database']}")
        return

    def concat_data(self, data):
        """读取并合并单品类永续合约的K线数据"""
        query = f"SELECT * FROM {data.contract}"
        df = pd.read_sql(query, self.engine)
        df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
        df.set_index('open_time', inplace=True)
        # 将所有K线数据合并
        data.data = df
        return


class Data():

    def __init__(self, contract):
        self.contract = contract
        return

 # In[2]


currency = 'ETHUSDT_15m_2022_2025'
d = Data(currency)
dm = DataManager()
dm.concat_data(d)
df = d.data.copy()

# In[3]
# 初始化空DataFrame存放因子
signals = pd.DataFrame()

# 计数器生成因子名
counter = 1

# 遍历i和j的组合
for i in [0, 1, 3, 5, 15]:
    for j in [1, 3, 5, 10, 20, 30, 60]:
        if i == j:
            continue

        # 计算因子：close的i步shift / j步shift，填充NaN为1
        factor = (df['close'].shift(i) / df['close'].shift(j)).fillna(1)

        # 生成因子名（hide001, hide002...）
        factor_name = f"hide{counter:03d}"

        # 将因子添加到signals中
        signals[factor_name] = factor

        counter += 1  # 递增计数器


# 计算相关系数矩阵
correlation_matrix = signals.corr()

# 设置画布大小
plt.figure(figsize=(20, 15))

# 绘制热力图
sns.heatmap(
    correlation_matrix,
    annot=True,          # 显示数值
    fmt=".2f",           # 数值格式（保留两位小数）
    cmap="coolwarm",     # 颜色映射（冷热对比色）
    vmin=-1, vmax=1,     # 固定颜色范围[-1, 1]
    linewidths=0.5,      # 单元格边线宽度
    cbar_kws={"shrink": 0.8}  # 调整颜色条大小
)

# 添加标题和调整布局
plt.title("Factor Correlation Matrix", fontsize=14)
plt.xticks(rotation=45, ha="right")  # 旋转x轴标签
plt.tight_layout()
plt.show()
# In[4]


df['r15'] = df['close'].shift(-15)/df['close']-1
df['r96'] = df['close'].shift(-96)/df['close']-1
df['r150'] = df['close'].shift(-150)/df['close']-1


# 合并 signals 和 df 中的目标列（确保索引对齐）
combined = pd.concat([signals, df[['r15', 'r96', 'r150']]], axis=1)

# 计算相关系数矩阵（IC矩阵）
ic_matrix = combined.corr().loc[signals.columns, ['r15', 'r96', 'r150']]


# In[5]

plt.figure(figsize=(12, 8))

# 计算 IC 的实际范围（去除极端值）
ic_min = ic_matrix.min().min()
ic_max = ic_matrix.max().max()
abs_max = max(abs(ic_min), abs(ic_max))  # 取绝对值最大的边界

sns.heatmap(
    ic_matrix,
    annot=True,
    fmt=".2f",
    cmap="coolwarm",
    vmin=-abs_max,   # 动态设置颜色范围
    vmax=abs_max,
    linewidths=0.5,
    cbar_kws={"shrink": 0.8}
)

plt.title("IC Heatmap (Adjusted Color Range)")
plt.show()


# In[6]


class TimeSeriesHomogeneityTest:
    def __init__(self, time_series, debug=False):
        """
        初始化类，传入时间序列数据。

        :param time_series: 时间序列数据，可以是list、numpy数组或pandas Series。
        """
        self.time_series = pd.Series(time_series)
        self.debug = debug

    def plot_time_series(self, window_size=None, max_windows=100, bins=200):
        """
        绘制时间序列的直方图，或者在不同时间窗口绘制直方图以便用户主观判断。

        :param window_size: 如果提供，将时间序列分成多个窗口并分别绘制。
        :param max_windows: 最大窗口数量，避免绘制过多子图。
        :param bins: 直方图的柱数。
        """
        if window_size:
            num_windows = min(len(self.time_series) //
                              window_size, max_windows)
            fig, axes = plt.subplots(
                num_windows, 1, figsize=(10, num_windows * 3))
            for i in range(num_windows):
                start = i * window_size
                end = start + window_size
                window_data = self.time_series[start:end]
                axes[i].hist(window_data, bins=bins, color='blue', alpha=0.7)
                axes[i].set_title(f'Window {i+1}')
            plt.tight_layout()
        else:
            plt.figure(figsize=(10, 6))
            plt.hist(self.time_series, bins=bins, color='blue', alpha=0.7)
            plt.title('Histogram of Time Series')
        plt.show()

    def compare_statistics(self, window_size, statistic='mean'):
        """
        比较不同时间窗口的统计量（如均值、方差等）是否一致。

        :param window_size: 时间窗口的大小。
        :param statistic: 要比较的统计量，可以是 'mean', 'var', 'skew', 'kurtosis' 等。
        :return: 各窗口的统计量。
        """
        num_windows = len(self.time_series) // window_size
        statistics = []
        for i in range(num_windows):
            start = i * window_size
            end = start + window_size
            window_data = self.time_series[start:end]
            if statistic == 'mean':
                statistics.append(window_data.mean())
            elif statistic == 'var':
                statistics.append(window_data.var())
            elif statistic == 'skew':
                statistics.append(window_data.skew())
            elif statistic == 'kurtosis':
                statistics.append(window_data.kurtosis())
            else:
                raise ValueError(f"Unsupported statistic: {statistic}")
        return statistics

    def hypothesis_test(self, window_size, test='ks'):
        """
        使用假设检验方法检验不同时间窗口的分布是否一致。

        :param window_size: 时间窗口的大小。
        :param test: 假设检验方法，可以是 'ks' (Kolmogorov-Smirnov) 或 'adf' (Augmented Dickey-Fuller)。
        :return: 检验结果的列表，每个元素包含窗口索引、检验方法、p 值、原假设和结论。
        """
        num_windows = len(self.time_series) // window_size
        test_results = []  # 用于保存每个窗口的检验结果

        for i in range(1, num_windows):
            window1 = self.time_series[(i-1) * window_size: i * window_size]
            window2 = self.time_series[i * window_size: (i + 1) * window_size]

            if test == 'ks':
                # Kolmogorov-Smirnov 检验：检验两个样本是否来自同一分布
                _, p_value = stats.ks_2samp(window1, window2)
                h0 = "H0: The distributions of the two samples are the same."
                conclusion = "Fail to reject H0 (Distributions are the same)" if p_value > 0.05 else "Reject H0 (Distributions are different)"
            elif test == 'adf':
                # Augmented Dickey-Fuller 检验：检验时间序列是否平稳
                adf_result = adfuller(window1 - window2)
                p_value = adf_result[1]
                h0 = "H0: The time series is non-stationary."
                conclusion = "Fail to reject H0 (Series is non-stationary)" if p_value > 0.05 else "Reject H0 (Series is stationary)"
            else:
                raise ValueError(f"Unsupported test: {test}")

            # 保存检验结果
            test_results.append({
                "Window Pair": f"{i} vs {i+1}",
                "Test": test,
                "H0": h0,
                "p-value": p_value,
                "Conclusion": conclusion
            })

        # 格式化输出检验结果
        if self.debug:
            for result in test_results:
                print(f"Window Pair: {result['Window Pair']}")
                print(f"Test: {result['Test']}")
                print(f"H0: {result['H0']}")
                print(f"p-value: {result['p-value']:.4f}")
                print(f"Conclusion: {result['Conclusion']}\n")

        return test_results

    def rolling_statistics(self, window_size, statistic='mean'):
        """
        计算滚动统计量，用于观察时间序列的稳定性。

        :param window_size: 滚动窗口的大小。
        :param statistic: 要计算的统计量，可以是 'mean', 'var', 'skew', 'kurtosis' 等。
        :return: 滚动统计量。
        """
        if statistic == 'mean':
            rolling_stat = self.time_series.rolling(window=window_size).mean()
        elif statistic == 'var':
            rolling_stat = self.time_series.rolling(window=window_size).var()
        elif statistic == 'skew':
            rolling_stat = self.time_series.rolling(window=window_size).skew()
        elif statistic == 'kurtosis':
            rolling_stat = self.time_series.rolling(window=window_size).kurt()
        else:
            raise ValueError(f"Unsupported statistic: {statistic}")
        return rolling_stat

    def plot_rolling_statistics(self, window_size, statistic='mean'):
        """
        绘制滚动统计量图，并添加均值线以便比较。

        :param window_size: 滚动窗口的大小。
        :param statistic: 要计算的统计量，可以是 'mean', 'var', 'skew', 'kurtosis' 等。
        """
        rolling_stat = self.rolling_statistics(window_size, statistic)
        plt.figure(figsize=(10, 6))
        plt.plot(rolling_stat, label=f'Rolling {statistic}')
        plt.axhline(rolling_stat.mean(), color='red',
                    linestyle='--', label=f'Mean {statistic}')
        plt.title(
            f'Rolling {statistic.capitalize()} with Window Size {window_size}')
        plt.legend()
        plt.show()
# In[7]


N = 96*7
heterfactor = []
homofactor = []


factors = list(signals.columns)
for factor in factors:
    ts_test = TimeSeriesHomogeneityTest(signals[factor])
    # ts_test.plot_rolling_statistics(window_size=3*N)
    p_mean = pd.DataFrame(ts_test.hypothesis_test(window_size=N))[
        'p-value'].mean()
    if p_mean <= 0.05:
        heterfactor.append(factor)
    else:
        homofactor.append(factor)

# In[8]
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd

# 设置画布和子图布局
n_cols = 4                # 每行显示4个子图
n_rows = (len(signals.columns) + n_cols - 1) // n_cols  # 计算需要的行数

plt.figure(figsize=(20, 5 * n_rows))  # 动态调整画布高度
plt.suptitle("Factor Distribution Histograms & KDE", y=1.02, fontsize=16)

# 遍历所有因子列
for idx, col in enumerate(signals.columns, 1):
    plt.subplot(n_rows, n_cols, idx)
    
    # 绘制直方图和KDE
    sns.histplot(
        signals[col], 
        kde=True,          # 同时显示核密度估计
        bins=50,          # 分箱数（根据数据量调整）
        color='skyblue',
        edgecolor='white',
        alpha=0.7
    )
    
    # 添加标题和坐标轴标签
    plt.title(f"{col} Distribution", fontsize=10)
    plt.xlabel("Value")
    plt.ylabel("Density")
    plt.grid(axis='y', linestyle='--', alpha=0.7)

plt.tight_layout()

# 保存为图片（可选）
# plt.savefig("factor_distributions.png", bbox_inches="tight", dpi=150)

plt.show()
# In[8]
import pandas as pd
a=pd.to_timedelta('15m')
