import os
import datetime

# 获取当前日期，格式为 "M.D"（例如 2.12）
today = datetime.datetime.now()
today_str = f"{today.month}.{today.day}"

# 桌面路径
desktop = os.path.join(os.path.expanduser("~"), "Desktop")

# “截图”文件夹路径
screenshot_folder = os.path.join(desktop, "截图")

# 如果"截图"文件夹不存在，则创建
if not os.path.exists(screenshot_folder):
    os.makedirs(screenshot_folder)

# 目标日期文件夹路径
folder_path = os.path.join(screenshot_folder, today_str)

# 检查日期文件夹是否存在，不存在则创建
if not os.path.exists(folder_path):
    os.makedirs(folder_path)
    print(f"已创建文件夹: {folder_path}")
else:
    print(f"文件夹已存在: {folder_path}")
