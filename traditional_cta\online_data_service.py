import requests
import pandas as pd
from typing import Dict, Any
import copy
import logging


class Dataservice():
    def __init__(self, symbol, interval: str = '15m', factor_names: list[str] = None):
        self.symbol = symbol
        self.interval = interval
        self.factor_names = factor_names
        self.BINANCE_API_URL = "https://fapi.binance.com/fapi/v1/klines"
        self.params = {
            "symbol": symbol,
            "interval": self.interval,
            "limit": 1500
        }
        self._init_kline()

    def _init_kline(self):
        res = requests.get(self.BINANCE_API_URL, self.params)

        self.kline = pd.DataFrame(res.json(), columns=['open_time', 'open', 'high', 'low', 'close', 'volume',
                                                       'close_time', 'turnover', 'trade_count', 'taker_buy_volume',
                                                       'taker_buy_turnover', 'ignore']).astype({
                                                           'open_time': 'object',
                                                           'open': 'float64',
                                                           'high': 'float64',
                                                           'low': 'float64',
                                                           'close': 'float64',
                                                           'volume': 'float64',
                                                           'close_time': 'object',
                                                           'turnover': 'float64',
                                                           'trade_count': 'int64',
                                                           'taker_buy_volume': 'float64',
                                                           'taker_buy_turnover': 'float64',
                                                           'ignore': 'object'
                                                       })

    def _update_kline(self, msg: Dict[str, Any]):
        """将解析后的实时K线数据追加到DataFrame"""
    # 去重：检查是否已存在相同 open_time 的K线
        parsed_kline = self._parse_msg(msg)
        existing = self.kline['open_time'] == parsed_kline['open_time']
        if not existing.any():
            # 追加新数据
            new_row = pd.DataFrame([parsed_kline])
            self.kline = pd.concat([self.kline, new_row], ignore_index=True)

            # 按时间排序并去重（防止乱序数据）
            self.kline = self.kline.sort_values(
                'open_time').drop_duplicates('open_time', keep='last')

            if len(self.kline) > 2000:
                # 删除旧数据
                self.kline = self.kline.iloc[-1000:]
            # 保留最近N条数据（可选）

        else:
            # 更新现有K线（如未闭合的实时K线）
            idx = existing.idxmax()
            self.kline.loc[idx] = parsed_kline

    def _parse_msg(self, msg: Dict[str, Any]) -> Dict[str, Any]:
        """解析K线数据并转换类型"""
        try:
            k = msg['k']
            return {
                'open_time': k['t'],
                'open': float(k['o']),
                'high': float(k['h']),
                'low': float(k['l']),
                'close': float(k['c']),
                'volume': float(k['v']),
                'close_time': k['T'],
                'turnover': float(k['q']),          # 对应 'q' 字段
                'trade_count': k['n'],              # 对应 'n' 字段（整数）
                'taker_buy_volume': float(k['V']),  # 对应 'V' 字段
                'taker_buy_turnover': float(k['Q']),  # 新增：对应 'Q' 字段
                'ignore': k['B'],                   # 新增：对应 'B' 字段（字符串）
                # 'datetime': pd.to_datetime(k['t'], unit='ms', utc=True)
            }
        except KeyError as e:
            logging.error(f"Missing key in signal: {e}")
            raise
        except ValueError as e:
            logging.error(f"Data conversion error: {e}")
            raise

    def get_kline(self):
        if not hasattr(self, 'kline'):
            raise AttributeError(
                "Kline not initialized. Please call _init_kline() first.")
        return self.kline
