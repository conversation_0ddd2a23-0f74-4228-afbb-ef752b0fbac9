import pandas as pd
coins = ['ETH', 'TON', '1000SHIB', 'VET',
         'RENDER', '1000PEPE', 'ATOM', 'ARB', 'OP']

# 用字典存储所有数据
df_dict = {}

for coin in coins:
    file_path = f'C:/Users/<USER>/python sc/workspace/coins_data/{coin}_r96.pkl'
    df_dict[coin] = pd.read_pickle(file_path)

# 取 ETH 作为基准序列
eth_series = df_dict['ETH']['ETH_r96']

# 存储相关性结果
corr_results = {}

for coin, df in df_dict.items():
    if coin != 'ETH':
        series = df[f'{coin}_r96']  # 选择 'price' 列
        corr = eth_series.corr(series)  # 计算相关性
        corr_results[coin] = corr

# 输出相关性结果
print(corr_results)
