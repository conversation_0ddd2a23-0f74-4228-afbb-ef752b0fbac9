import json
import copy

import pandas as pd
from datetime import datetime
import time


class BackTesterTwoWay:
    def __init__(self,
                 data_handler,
                 execution_handler,
                 performance_metrics,
                 visualizer,
                 config):
        """
        初始化回测类
        :param data_handler: 数据处理类，用于获取数据并划分训练集、验证集、测试集
        :param execution_handler: 执行处理类，负责交易执行及仓位管理
        :param performance_metrics: 性能评估类，计算回测指标并打印结果
        :param visualizer: 可视化类，用于绘制回测结果
        :param config: 配置字典，包含回测相关的配置参数，如初始资金等
        """
        self.data_handler = data_handler
        self.execution_handler = execution_handler
        self.performance_metrics = performance_metrics
        self.visualizer = visualizer
        self.config = config

        self.exercise = []
        self.verification = []
        self.test = []
        return

    def run_backtest(self):
        """
        执行回测
        该方法负责整体回测流程的执行，
        包括数据划分、训练集、验证集、测试集回测，以及回测结果的评估与可视化。

        :param None: 无
        :return: None
        """
        # 数据清洗
        self.data_handler.clean_data()

        # 数据划分
        train_data, valid_data, test_data = self.data_handler.split_data()

        # 回测训练集
        print("Running backtest on the training set:")
        self.run_single_backtest(train_data, "train_data_log2_two_way.txt")

        # 回测验证集
        print("Running backtest on the validation set:")
        self.run_single_backtest(valid_data, "valid_data_log2_two_way.txt")

        # 回测测试集
        print("Running backtest on the test set:")
        self.run_single_backtest(test_data, "test_data_log2_two_way.txt")

    def run_single_backtest(self, data, file_name):
        execution_handler = copy.deepcopy(self.execution_handler)
        """
        执行单次回测
        该方法负责对某一数据集进行回测，计算账户净值和每日收益。

        :param data: 回测数据集（训练集、验证集或测试集）
        :type data: pandas.DataFrame
        :return: None
        """
        if len(data) == 0:
            print("data 数据长度为 0 .")
            return

        start_time = time.time()

        # 初始化初始资金
        initial_capital = self.config['initial_capital']

        # 准备净值曲线和每日收益的初始状态
        equity_curve_transitory = []
        equity_curve_transitory2 = []
        # 遍历数据集，获取每天的交易信号
        data_copy = data.copy()
        data_copy['close_time_str'] = data_copy['close_time'].apply(
            convert_milliseconds_to_datetime)

        filtered_data = data_copy.copy()
        filtered_data.reset_index(drop=True, inplace=True)
        # 只处理因子信号不为0的数据
        # filtered_data = filtered_data[filtered_data['signals'] != 0]

        for index, row in filtered_data.iterrows():
            price = row['close']  # 获取当天价格
            signal = row['signals']  # 获取交易信号
            close_time = row['close_time']
            close_time_str = row['close_time_str']

            # 判断当前仓位
            long_now_value = 0
            short_now_value = 0
            # 有多仓
            if execution_handler.position['long_num'] > 0:
                # 多仓 -计算：(当前价格 - 开仓均价) * 仓位数量 + 开仓价值(开仓成本)
                long_now_value = ((price - execution_handler.position['long_avg_cost']) * execution_handler.position['long_num'] +
                                  execution_handler.position['long_num'] * execution_handler.position['long_avg_cost'])
            # 有空仓
            if execution_handler.position['short_num'] < 0:
                # 空仓 -计算：-(当前价格 - 开仓均价) * 仓位数量 + 开仓价值(开仓成本)
                short_now_value = -(price - execution_handler.position['short_avg_cost']) * abs(
                    execution_handler.position['short_num']) + abs(
                    execution_handler.position['short_num'] * execution_handler.position['short_avg_cost'])
            # 当前帧 含浮盈浮亏的价值余额
            balance_price = execution_handler.position['available_cash'] + \
                long_now_value + short_now_value
            # 使用交易执行处理器执行交易
            volume = 100  # 假设默认每次交易100个单位，可以改为你的交易策略决定的量
            execution_handler.execute_trade(signal, price, volume, close_time)

            equity_curve_transitory.append(
                [close_time_str, round(float(balance_price), 4)])
            equity_curve_transitory2.append(
                [close_time, round(float(balance_price), 4)])

        time_val = pd.DataFrame(equity_curve_transitory, columns=[
                                'close_time_str', 'val'])
        time_val2 = pd.DataFrame(equity_curve_transitory2, columns=[
                                 'close_time', 'val'])

        # 不需要补数据
        equity_curve = time_val.set_index('close_time_str')[
            'val'].astype('float64')
        equity_curve2 = time_val2.set_index(
            'close_time')['val'].astype('float64')

        print("---------------记录数据-------")
        with open('equity_curve＿log2_two_way.txt', 'w', encoding='utf-8') as f:
            json.dump(equity_curve2.to_dict(), f, ensure_ascii=False, indent=4)

        end_time = time.time()
        execution_time = end_time - start_time
        print(f"代码块执行时间: {execution_time:.4f} 秒，数量：{len(equity_curve)}")
        # print(f"：{equity_curve}")

        # 计算每日收益
        daily_returns = equity_curve.pct_change().dropna()

        # 评估性能
        self.performance_metrics.calculate(equity_curve)
        self.performance_metrics.print_results()

        # 可视化
        self.visualizer.plot_equity_curve(equity_curve)
        self.visualizer.plot_drawdown(equity_curve)
        self.visualizer.plot_daily_returns(daily_returns)
        self.visualizer.plot_return_distribution(daily_returns)

        # execution_handler.print_trade_log()

        print("---------------记录数据-------")
        with open(file_name, 'w', encoding='utf-8') as f:
            json.dump(execution_handler.get_trade_log(),
                      f, ensure_ascii=False, indent=4)


# 定义一个函数，将毫秒转换为日期时间字符串
def convert_milliseconds_to_datetime(ms):
    # 将毫秒转换为秒
    seconds = ms / 1000.0
    # 转换为 datetime 对象
    dt = datetime.fromtimestamp(seconds)
    # 格式化为 'YYYY-MM-DD HH:MM:SS' 字符串
    return dt.strftime('%Y-%m-%d %H:%M:%S')

# 定义一个函数，将日期时间字符串转换为毫秒


def convert_datetime_to_milliseconds(date_time):
    dt = datetime.strptime(date_time, "%Y/%m/%d %H:%M:%S")
    # 转换为秒级时间戳（浮点数），再乘以 1000 得到毫秒
    timestamp_ms = int(dt.timestamp() * 1000)
    # 格式化为 'YYYY-MM-DD HH:MM:SS' 字符串
    return timestamp_ms
