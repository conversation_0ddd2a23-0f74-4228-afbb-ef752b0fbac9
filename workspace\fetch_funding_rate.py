import aiohttp
from itertools import chain
import asyncio
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')

# Binance API URL
BINANCE_API_URL = "https://fapi.binance.com/fapi/v1/fundingRate"

# MySQL数据库配置
MYSQL_CONFIG = {
    "host": "************",
    "user": "root",
    "password": "1234",
    "database": "ctadata"
}


async def fetch_klines(session, symbol, start_time, end_time):
    params = {
        "symbol": symbol,
        'startTime':start_time,
        'endTime':end_time,
        "limit": 1000
        
    }
    try:
        async with session.get(BINANCE_API_URL, params=params) as response:
            response.raise_for_status()  # 检查请求是否成功
            return await response.json()
    except aiohttp.ClientError as e:
        logging.error(f"Failed to fetch klines: {e}")
        return []


async def main(symbol, start_year, end_year):

    async with aiohttp.ClientSession() as session:
        tasks = []
        completed_data = []

        day_timestamps = get_year_timestamps(start_year, end_year)
        for i in range(len(day_timestamps) - 1):
            start_time = day_timestamps[i][0]
            end_time = day_timestamps[i][1]

            tasks.append(fetch_klines(session, symbol, start_time, end_time))
            if (i + 1) % 60 == 0:
                results = await asyncio.gather(*tasks)
                completed_data = list(chain.from_iterable(results))
                if completed_data:
                    logging.info(
                        f"Inserted {len(completed_data)} klines to database.")

                tasks = []
                completed_data = []  # 清空数据

        # 处理最后一批数据
        results = await asyncio.gather(*tasks)
        completed_data = list(chain.from_iterable(results))
        if completed_data:
            logging.info(f"Inserted {len(completed_data)} klines to database.")


# 输入年份，返回该年份所有日期的开始和结束时间戳对的列表


def get_year_timestamps(start_year, end_year):
    start = datetime(start_year, 1, 1)
    end = datetime(end_year + 1, 1, 1)
    timestamps = []
    while start < end:
        timestamps.append((int(start.timestamp() * 1000),
                          int((start + timedelta(days=1)).timestamp() * 1000 - 1)))
        start += timedelta(days=1)
    return timestamps


if __name__ == "__main__":
    symbol = "BTCUSDT"
    start_year = 2023  # 修改为所需的开始年份
    end_year = 2024    # 修改为所需的结束年份
    asyncio.run(main(symbol, start_year, end_year))
