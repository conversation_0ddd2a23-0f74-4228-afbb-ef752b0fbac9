import back_tester
import back_tester_all
import back_tester_lack
import data_handler
import statistics_handler
import visualizer
import execution_handler

import os
import pickle

import pandas as pd
import numpy as np
import plotly.graph_objects as go
import requests
from plotly.subplots import make_subplots
from sqlalchemy import create_engine


MYSQL_CONFIG = {
    "host": "**************:33306",
    "user": "root",
    "password": "1234",
    "database": "ctadata"
}


class DataManager():

    def __init__(self):
        # self.__tim__ = ticker.TickerInfoManager()
        self.problem_ticker = []
        self.engine = create_engine(
            f"mysql+pymysql://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}@{MYSQL_CONFIG['host']}/{MYSQL_CONFIG['database']}")

        self.data_dir = 'data/'  # 存储数据的目录

        return

    def concat_data(self, data):
        # 定义持久化文件名
        data_file = os.path.join(self.data_dir, f"{data.contract}.pkl")
        print(f"K线数据读取：{data.contract}")

        # 如果存在本地文件，则读取
        if os.path.exists(data_file):
            with open(data_file, 'rb') as f:
                df = pickle.load(f)
                df = df.dropna(subset=['close'])
            print(f"从本地加载k线数据数据->{data_file}")

            data.data = df
        else:
            """读取并合并单品类永续合约的K线数据"""
            query = f"SELECT * FROM {data.contract}"
            df = pd.read_sql(query, self.engine)
            df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
            df.set_index('open_time', inplace=True)
            df = df.dropna(subset=['close'])
            # 将所有K线数据合并
            data.data = df

            # 将获取的数据保存到本地
            with open(data_file, 'wb') as f:
                pickle.dump(df, f)
            print(f"从在线获取k线数据数据并保存本地->{data_file}")
            print("\n")

            return


class Data():

    def __init__(self, contract):
        self.contract = contract
        return


class EventAnal():

    def __init__(self):
        return

    def get_data(self, d):
        global settings

        td = d.data
        return td.iloc[settings["from_data"]:None if settings["to_data"] == 0 else settings["to_data"]].reset_index(
            drop=True)

    def find_ticker_event(self, d, sig, flt, sign, is_absolute=False):
        global settings

        """查找事件并计算收益"""
        td = d.data.iloc[settings["from_data"]:None if settings["to_data"] == 0 else settings["to_data"]].reset_index(
            drop=True)
        s = sig(td)
        fs = flt(td)
        if sign != 0:
            el = s[s == sign]
        else:
            el = s[abs(s) == 1]

        ret = {}
        fret = {}

        for i in el.index:
            eloc = td.index.get_loc(i)
            e = (td.close.iloc[-1200 + eloc:1200 + eloc] / td.close.iloc[eloc] - 1) * el[i]
            e.index = range(-1200, len(e) - 1200)
            ret[i] = e
            if is_absolute:
                fret[i] = fs.loc[i]
            else:
                fret[i] = fs.loc[i] * el[i]
        ret = pd.DataFrame(ret)
        fret = pd.Series(fret)
        return ret, fret

    def find_event(self, d, sig, flt, sign, is_absolute=False):
        ret, fret = self.find_ticker_event(d, sig, flt, sign, is_absolute)
        return ret, fret

    def plot_event(self, d, sig):
        td = d.data
        s = sig(td)
        s1 = td.close.reset_index(drop=True)
        s2 = td.close.where((s == 1), np.nan).reset_index(drop=True).plot(marker='^')
        s3 = td.close.where((s == -1), np.nan).reset_index(drop=True).plot(marker='v')
        s1.plot(color='black', alpha=0.5);
        s2.plot(color='red');
        s3.plot(color='blue')
        return


def fetch(df):
    global settings

    factor_file = f"data/{settings['method']}_{settings['table']}.pkl"
    print()

    if os.path.exists(factor_file):
        with open(factor_file, 'rb') as f:
            series = pickle.load(f)
        print(f"从本地加载因子数据->{factor_file}。")
    else:
        response = requests.get(
            "http://**************:56678",
            params={
                "method": settings["method"],
                "table": settings["table"]
            }
        )

        print(f"status: {response.status_code}")

        json_data = response.json()
        series = pd.Series(json_data)
        series.index = pd.to_datetime(series.index.astype(float), unit='ms')

        # 将获取的数据保存到本地
        with open(factor_file, 'wb') as f:
            pickle.dump(series, f)
        print(f"从在线获取因子数据并保存本地->{factor_file}。")

    signals = series.iloc[settings["from_data"]:None if settings["to_data"] == 0 else settings["to_data"]].reset_index(
        drop=True)

    # 》》》》》》》》》》》》》》》》》》》》》》》》》
    # 》》》》》》》》》》》》》》》》》》》》》》》》》 过滤条件
    # 》》》》》》》》》》》》》》》》》》》》》》》》》
    # 过滤1：快慢线比例:filter_1
    # 过滤2：相对高低比例：filter_2

    # start_filter = settings["start_filter"]
    # filter_array = settings["filter_array"]
    # if not (len(start_filter) == 0):
    #     for i in range(len(filter_array)):
    #         F_f = filter_array[i + 1]
    #         f_0 = F_f(df)
    #         f_0_lower = f_0.expanding().quantile(0.2)  # 只要f1的前20%（开多时）0~0.2
    #         f_0_upper = f_0.expanding().quantile(0.8)  # 只要f1的后20%（开空时） 0.8~1
    #         signals[f_0 > f_0_lower] = 0
    #         # signals[f_0 < f_0_upper] = 0

    bottom_filter = settings["bottom_filter"]
    f_F = settings["filter_array"][bottom_filter[0] - 1]
    f_0 = f_F(df)
    f_0_lower = f_0.expanding().quantile(0.2)  # 只要f1的前20%（开多时）0~0.2
    f_0_upper = f_0.expanding().quantile(0.8)  # 只要f1的后20%（开空时） 0.8~1
    x_i = bottom_filter[1]
    if x_i == 0:
        signals[f_0 > f_0_lower] = 0
    elif x_i == 1:
        signals[f_0 < f_0_upper] = 0
    else:
        print("")

    # filter_array = settings["filter_array"]
    # for i in range(len(filter_array)):
    #     F_f_row = filter_array[i][0]
    #     x_ii = filter_array[i][1]
    #     f_i = F_f_row(df)
    #     f_i_lower = f_i.expanding().quantile(0.2)  # 只要f1的前20%（开多时）0~0.2
    #     f_i_upper = f_i.expanding().quantile(0.8)  # 只要f1的后20%（开空时） 0.8~1
    #     if x_ii == 0:
    #         signals[f_0 > f_i_lower] = 0
    #     elif x_ii == 1:
    #         signals[f_0 < f_i_upper] = 0
    #     else:
    #         print("")

    return signals


# 过滤器：快慢线比例
def filter_1(df):
    c = df.close

    # 衡量短线波动快慢的，快慢线比例
    short_T = 5
    long_T = 50
    ma_short = c.rolling(window=short_T).mean()
    ma_long = c.rolling(window=long_T).mean()
    return ma_short / ma_long


# 过滤器：相对位置高低比例
def filter_2(df):
    c = df.close
    h = df.high
    l = df.low

    # df['relative_position'] = ((df['current_price'] - df['low_price']) /
    #                            (df['high_price'] - df['low_price'])) * 100
    req = ((c - l) / (h - l))
    return req


# 过滤器：当前波动高低比例
def filter_3(df):
    c = df.close
    h = df.high
    l = df.low

    req = (h - l) / l
    return req


# 过滤器：当前成交量高低
def filter_4(df):
    c = df.close
    h = df.high
    l = df.low
    v = df.volume

    highest_volume = v.tail(10).max()
    lowest_volume = v.tail(10).min()
    # z1 = h.rolling(window=10).max()
    # z2 = l.rolling(window=10).min()
    req = (v - lowest_volume) / (highest_volume - lowest_volume)
    return req


def filter_hub(df):
    f_F = settings["filter_array"][settings["bottom_filter"][0] - 1]
    s = f_F(df)
    # s = filter_2(df)

    # start_filter = settings["start_filter"]
    # filter_array =
    # if len(start_filter) == 0:
    #     s = filter_array[0](df)
    # else:
    #     s = filter_array[start_filter[0] - 1](df)

    s_min = s.min()
    s_max = s.max()
    print(f"s 的范围是 [{s_min}, {s_max}]")

    return s


settings = {
    # 你要回测的数据
    "table": "BTCUSDT_15m_2020_2021",
    # 你要调用的因子方法
    "method": "ret_ma_short_long_cross_sig_price",
    # 切片，from
    "from_data": -9999999,
    # 切片，to，如果填0就切到最后一个
    "to_data": 0,
    # 是否显示过滤图
    "show_filter": True,
    # "show_filter": False,
    # 是否显示k线图
    # "show_k_line": True,
    "show_k_line": False,

    # 使用 函数
    "bottom_filter": [2, 0],
    # 额外使用函数
    "filter_array": [filter_1, filter_2, filter_3, filter_4],
}




def position_management_fn(signals, price, position, cash, config):
    """
    每次买卖固定0.001个币
    """
    trade_volume = 1
    executed_price = price
    fee = 0.0005

    slippage = config.get('slippage', 0.002)
    fee_rate = config.get('fee_rate', 0.0003)
    trade_volume = 0.01

    return trade_volume, executed_price, fee


def fun_backtester(data):
    # 数据处理类
    datahandler = data_handler.DataHandler(data, config={
        'train_ratio': 0.3,
        'valid_ratio': 0,
        'test_ratio': 0.7,
    })
    # 交易执行引擎
    executionhandler = execution_handler.ExecutionHandler(
        position_management_fn=position_management_fn,
        config={
            'initial_capital': 5000,  # 初始资金
            'slippage': 0.2,  # 滑点比例
            'fee_rate': 0.03  # 手续费率
        }
    )
    # 生成可视化类
    visualizerh = visualizer.Visualizer()
    # 计算统计类
    performance_metrics = statistics_handler.StatisticsHandler()

    # 回测回测类
    backtester = back_tester.BackTester(datahandler,
                                        executionhandler,
                                        performance_metrics,
                                        visualizerh,
                                        config={
                                            'initial_capital': 5000,
                                            'balance': 5000,
                                            'exchange': 'binance',
                                            'contract_type': "swap",
                                            'lever': '1'
                                        })

    backtester.run_backtest()

def fun_backtester_all(data):
    # 数据处理类
    datahandler = data_handler.DataHandler(data, config={
        'train_ratio': 0.1,
        'valid_ratio': 0.2,
        'test_ratio': 0.7,
    })
    # 交易执行引擎
    executionhandler = execution_handler.ExecutionHandler(
        position_management_fn=position_management_fn,
        config={
            'initial_capital': 5000,  # 初始资金
            'slippage': 0.2,  # 滑点比例
            'fee_rate': 0.03  # 手续费率
        }
    )
    # 生成可视化类
    visualizerh = visualizer.Visualizer()
    # 计算统计类
    performance_metrics = statistics_handler.StatisticsHandler()

    # 回测回测类
    backtester_all = back_tester_all.BackTester(datahandler,
                                        executionhandler,
                                        performance_metrics,
                                        visualizerh,
                                        config={
                                            'initial_capital': 5000,
                                            'balance': 5000,
                                            'exchange': 'binance',
                                            'contract_type': "swap",
                                            'lever': '1'
                                        })

    backtester_all.run_backtest()

def fun_backtester_lack(data):
    # 数据处理类
    datahandler = data_handler.DataHandler(data, config={
        'train_ratio': 0.1,
        'valid_ratio': 0,
        'test_ratio': 0.7,
    })
    # 交易执行引擎
    executionhandler = execution_handler.ExecutionHandler(
        position_management_fn=position_management_fn,
        config={
            'initial_capital': 5000,  # 初始资金
            'slippage': 0.2,  # 滑点比例
            'fee_rate': 0.03  # 手续费率
        }
    )
    # 生成可视化类
    visualizerh = visualizer.Visualizer()
    # 计算统计类
    performance_metrics = statistics_handler.StatisticsHandler()

    # 回测回测类
    backtester_lack = back_tester_lack.BackTester(datahandler,
                                        executionhandler,
                                        performance_metrics,
                                        visualizerh,
                                        config={
                                            'initial_capital': 5000,
                                            'balance': 5000,
                                            'exchange': 'binance',
                                            'contract_type': "swap",
                                            'lever': '1'
                                        })

    backtester_lack.run_backtest()

def main_1():
    d = Data(settings["table"])
    dm = DataManager()
    dm.concat_data(d)
    print("K线数据数据总条数：", len(d.data))

    ea = EventAnal()
    td = ea.get_data(d)
    s = fetch(td)

    # 模拟入场-信号
    # td['signals'] = np.random.choice([0, -1, 1], size=len(td))
    td['signals'] = s
    data = td

    # 执行回测
    fun_backtester(data)


    # 对比优化前后
    print("----- 全部信号---------------------------------------")
    # 所有信号遍历
    # fun_backtester_all(data)
    print("----- 过滤信号---------------------------------------")
    # 入场信号遍历
    # fun_backtester_lack(data)




if __name__ == '__main__':
    main_1()






