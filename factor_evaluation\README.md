# 因子评估系统

这个系统用于评估交易因子的有效性和特性，支持不同币种和时间周期的分析。

## 功能

- 平稳性检验（ADF检验）
- 相关性分析（IC和Rank IC）
- 信息比率（IR）计算
- 分组收益分析
- 因子分布分析

## 使用方法

### 基本用法

```python
from factor_evaluator import FactorEvaluator

# 创建评估器实例
evaluator = FactorEvaluator(
    currency='ETHUSDT_15m_2020_2025',  # 币种_时间周期_起始年_结束年
    start_date='2021-10-1',            # 分析起始日期
    return_period=96                   # 收益率计算周期
)

# 加载数据
evaluator.load_data()

# 运行分析
evaluator.run_analysis(show_plots=True)
```

### 分析不同币种

```python
# 定义要分析的币种
currency = 'BTCUSDT_15m_2020_2025'

# 创建评估器实例
evaluator = FactorEvaluator(
    currency=currency,
    start_date='2021-10-1'
)

# 运行分析
evaluator.run_analysis()
```

### 分析不同时间段

```python
# 定义要分析的时间段
currency = 'ETHUSDT_15m_2020_2025'
start_date = '2022-01-01'
end_date = '2022-12-31'

# 创建评估器实例
evaluator = FactorEvaluator(
    currency=currency,
    start_date=start_date,
    end_date=end_date
)

# 运行分析
evaluator.run_analysis()
```

## 参数说明

### FactorEvaluator 类初始化参数

| 参数 | 类型 | 说明 | 默认值 |
|------|------|------|--------|
| currency | str | 币种代码，格式如 'ETHUSDT_15m_2020_2025' | None |
| start_date | str | 起始日期，格式如 '2021-10-1' | None |
| end_date | str | 结束日期，格式如 '2022-10-1' | None |
| return_period | int | 收益率计算周期 | 96 |

### run_analysis 方法参数

| 参数 | 类型 | 说明 | 默认值 |
|------|------|------|--------|
| show_plots | bool | 是否显示图表 | True |
