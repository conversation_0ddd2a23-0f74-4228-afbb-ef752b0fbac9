import re

# 读取文件内容
file_path = 'vnpy_backtester/objects/object.py'
with open(file_path, 'r', encoding='utf-8') as f:
    content = f.read()

# 替换 datetime: datetime
content = re.sub(r'datetime: datetime(?!\s*=)', 'datetime: dt', content)

# 替换 datetime: datetime = None
content = re.sub(r'datetime: datetime\s*=\s*None', 'datetime: Optional[dt] = None', content)

# 写回文件
with open(file_path, 'w', encoding='utf-8') as f:
    f.write(content)

print('替换完成')
