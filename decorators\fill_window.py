import functools
import pandas as pd


def fill_window(window=100):
    '''装饰器，对因子进行信号填充'''
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            signals = func(*args, **kwargs)  # 调用原函数获取 Series

            s_modified = pd.Series(0, index=signals.index)  # 初始化全0

            idx_1 = signals[signals == 1].index
            idx_neg1 = signals[signals == -1].index

            s_pos = pd.Series(0, index=signals.index)
            s_neg = pd.Series(0, index=signals.index)

            for i in idx_1:
                # 获取时间戳 i 在 signals 中的位置索引
                pos = signals.index.get_loc(i)
                s_pos.iloc[pos:pos + window] = 1  # 让后续100个点变成 1

            for i in idx_neg1:
                # 获取时间戳 i 在 signals 中的位置索引
                pos = signals.index.get_loc(i)
                s_neg.iloc[pos:pos + window] = -1  # 让后续100个点变成 -1

            s_modified = s_pos + s_neg
            return s_modified

        return wrapper
    return decorator
