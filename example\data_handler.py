import pandas as pd
import numpy as np
import math


class DataHandler:
    """
    数据处理类，主要负责数据的加载、清洗和划分训练、验证、测试集。
    :param data: 输入数据(暂定为DataFrame)
    :param config: 配置文件，包含训练、验证和测试集的比例等信息
    """

    def __init__(self, data, config):
        """
        数据处理器
        功能：专门负责数据的准备和分割工作
        参数说明：
        data → 原始数据集
        config → 设置参数，包含:
            - train_ratio: 训练集比例（0-1之间，比如0.1表示10%）
            - valid_ratio: 验证集比例（0-1之间，比如0.2表示20%）这三个比例值之和应该等于1
            - test_ratio: 测试集比例 （0-1之间，比如0.7表示70%）
        """
        self.data = data
        self.config = config

    """
    清洗数据：检测缺失值并进行处理，发现异常值时报错退出
    可以加入更多数据检测的逻辑
    """

    def clean_data(self):
        """
        [数据清洗] 处理数据中的空缺和异常
        处理流程：
        1. 缺失值处理 → 是否有空值
        2. 异常值检测 → 使用3倍标准差法找出异常波动数据
        3. 日期排序检查 → 确保数据按时间顺序排列
        （发现异常会直接报错提醒）
        """
        # 检查是否存在缺失值
        if self.data.isnull().values.any():
            raise ValueError('数据中存在缺失值，请检查数据源')

        # 异常值检测（3σ原则）
        numeric_cols = self.data.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            mean = self.data[col].mean()
            std = self.data[col].std()
            outliers = (self.data[col] - mean).abs() > 3 * std
            # if outliers.any():
            #     raise ValueError(f'发现异常值在列 {col}')

        # 检查数据是否包含日期列
        if 'date' in self.data.columns:
            # 检查日期列是否为日期时间类型
            if not pd.api.types.is_datetime64_any_dtype(self.data['date']):
                # 尝试将字符串格式的日期转换为日期时间类型
                try:
                    self.data['date'] = pd.to_datetime(self.data['date'])
                except:
                    raise ValueError(f"发现异常, 无法将'date'列转换为日期格式")

            # 检查数据是否按日期排序（单调递增）
            if not self.data['date'].is_monotonic_increasing:
                raise ValueError(f'发现异常, 数据未按时间排序')

    """
    划分数据为训练集、验证集和测试集
    :return: 训练集、验证集和测试集
    """

    def split_data(self):
        """
        [数据切分] 把数据按顺序分成三份（训练集→验证集→测试集）
        特别说明：
        - 训练集：用于模型学习历史规律
        - 验证集：用于调整模型参数
        - 测试集：最后检验模型真实效果
        """
        # 第一步：获取数据总量和划分比例
        total_size = len(self.data)  # 数据总量
        # 从配置中获取训练集比例，默认为0.7（70%）
        train_ratio = self.config.get('train_ratio', 0.7)
        # 从配置中获取验证集比例，默认为0.15（15%）
        valid_ratio = self.config.get('valid_ratio', 0.15)
        # 从配置中获取测试集比例，默认为0.15（15%）
        test_ratio = self.config.get('test_ratio', 0.15)
        # 初始化训练集、验证集和测试集
        train_data, valid_data, test_data = None, None, None

        # 第二步：检查并修正比例
        # 检查比例总和是否为1，允许小的浮点误差（1e-10）
        if abs(train_ratio + valid_ratio + test_ratio - 1.0) > 1e-10:
            raise ValueError(f'发现参数异常, 数据集比例总和不为1')

        # 第三步：计算各数据集的大小
        train_size = int(math.ceil(total_size * train_ratio))  # 训练集大小
        valid_size = int(math.ceil(total_size * valid_ratio))  # 验证集大小

        # 第四步：按时间顺序划分（适用于时间序列数据）
        # 取前train_size条记录作为训练集
        train_data = self.data[:train_size]
        # 取接下来valid_size条记录作为验证集
        valid_data = self.data[train_size:train_size + valid_size]
        # 取剩余记录作为测试集
        test_data = self.data[train_size + valid_size:]

        # 第五步：返回划分结果
        return train_data, valid_data, test_data

    """
    获取滚动窗口数据
    :param start_idx: 窗口的起始索引
    :param window_size: 窗口大小
    :return: 窗口数据
    """

    def get_window_data(self, start_idx, window_size):
        """
        [时间窗口取数] 获取指定时间段的数据片段
        参数说明：
        start_idx → 起始位置
        window_size → 需要获取的数据长度

        安全机制：起始位置超出数据总量时会自动报错提醒
        """
        # 第一步：检查起始索引是否有效
        if start_idx < 0 or start_idx >= len(self.data):
            # 如果索引超出范围，抛出异常
            raise IndexError(
                f"起始索引 {start_idx} 超出数据范围 [0, {len(self.data) - 1}]")

        # 第二步：计算结束索引，确保不超出数据范围
        end_idx = min(start_idx + window_size, len(self.data))
        # 获取窗口数据的副本，避免对原始数据的意外修改
        window_data = self.data.iloc[start_idx:end_idx].copy()

        # 第三步：检查是否获取到了完整的窗口数据
        if end_idx - start_idx < window_size:
            # 如果实际获取的数据量小于请求的窗口大小，发出警告
            print(
                f"警告：请求的窗口大小为 {window_size}，但实际只取到了 {end_idx - start_idx} 条数据")
        # 返回窗口数据
        return window_data
