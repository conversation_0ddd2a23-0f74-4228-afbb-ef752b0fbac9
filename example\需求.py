import pandas as pd
import numpy as np
from enum import Enum
from datetime import datetime


class SignalType(Enum):
    """交易信号类型

    枚举值:
        BUY (1): 买入信号，开多仓
        SELL (-1): 卖出信号，开空仓
        CLOSE_LONG (2): 平多信号，关闭多头仓位
        CLOSE_SHORT (-2): 平空信号，关闭空头仓位
        NO_ACTION (0): 无操作信号，维持当前仓位
    """
    BUY = 1       # 买入
    SELL = -1     # 卖出
    CLOSE_LONG = 2  # 平多
    CLOSE_SHORT = -2  # 平空
    NO_ACTION = 0  # 无操作


class Strategy:
    """策略基类"""

    def __init__(self, window_size=30):
        """初始化策略

        参数:
            window_size (int): 策略计算所需的K线窗口大小，默认为30
        """
        self.window_size = window_size

    def execute(self, klines):
        """执行策略并返回交易信号

        参数:
            klines (pandas.DataFrame): K线数据，包含'open', 'high', 'low', 'close', 'volume'列

        返回:
            dict: 包含以下键的字典:
                - signal (SignalType): 交易信号类型
                - volume (float): 交易量
                - price (float): 当前价格

        异常:
            ValueError: 当K线数据长度小于所需窗口大小时抛出
        """
        # 检查K线数据
        if len(klines) < self.window_size:
            raise ValueError(f"K线数据不足，需要至少{self.window_size}根K线")

        # 默认实现返回无操作
        return {
            'signal': SignalType.NO_ACTION,
            'volume': 0.0,
            'price': klines['close'].iloc[-1]
        }


class MAStrategy(Strategy):
    """均线交叉策略"""

    def __init__(self, fast_period=10, slow_period=20):
        """初始化均线策略

        参数:
            fast_period (int): 快速均线周期，默认为10
            slow_period (int): 慢速均线周期，默认为20
        """
        super().__init__(max(fast_period, slow_period) + 5)
        self.fast_period = fast_period
        self.slow_period = slow_period
        # 记录持仓状态
        self.position = 0  # 0表示无仓位，1表示多头，-1表示空头
        self.entry_time = None  # 入场时间
        self.position_bars = 0  # 持仓K线数

    def execute(self, klines):
        """根据均线交叉生成交易信号

        参数:
            klines (pandas.DataFrame): K线数据，包含'open', 'high', 'low', 'close', 'volume'列

        返回:
            dict: 包含以下键的字典:
                - signal (SignalType): 交易信号类型
                - volume (float): 交易量
                - price (float): 当前价格
                - position (int): 当前持仓状态，0表示无仓位，1表示多头，-1表示空头
                - bars_held (int): 持仓K线数
        """
        super().execute(klines)

        # 计算均线
        fast_ma = klines['close'].rolling(window=self.fast_period).mean()
        slow_ma = klines['close'].rolling(window=self.slow_period).mean()

        # 判断交叉
        current_fast, prev_fast = fast_ma.iloc[-1], fast_ma.iloc[-2]
        current_slow, prev_slow = slow_ma.iloc[-1], slow_ma.iloc[-2]

        # 默认无操作
        signal = SignalType.NO_ACTION
        volume = 0.0

        # 更新持仓K线计数
        if self.position != 0:
            self.position_bars += 1

        # 持仓10根K线后平仓
        if self.position_bars >= 10:
            if self.position == 1:  # 持有多头
                signal = SignalType.CLOSE_LONG
                volume = 1.0
            elif self.position == -1:  # 持有空头
                signal = SignalType.CLOSE_SHORT
                volume = 1.0

            # 重置持仓状态
            self.position = 0
            self.position_bars = 0
            self.entry_time = None

        # 如果没有触发平仓，检查是否有新的交易信号
        elif signal == SignalType.NO_ACTION:
            # 金叉买入
            if prev_fast < prev_slow and current_fast > current_slow:
                # 如果已经持有空头，先平空
                if self.position == -1:
                    signal = SignalType.CLOSE_SHORT
                    volume = 1.0
                    # 重置持仓状态，下一次迭代会产生买入信号
                    self.position = 0
                    self.position_bars = 0
                # 如果没有持仓，则开多
                elif self.position == 0:
                    signal = SignalType.BUY
                    volume = 1.0
                    # 更新持仓状态
                    self.position = 1
                    self.position_bars = 0
                    self.entry_time = klines.index[-1] if isinstance(
                        klines.index, pd.DatetimeIndex) else len(klines) - 1

            # 死叉卖出
            elif prev_fast > prev_slow and current_fast < current_slow:
                # 如果已经持有多头，先平多
                if self.position == 1:
                    signal = SignalType.CLOSE_LONG
                    volume = 1.0
                    # 重置持仓状态，下一次迭代会产生卖出信号
                    self.position = 0
                    self.position_bars = 0
                # 如果没有持仓，则开空
                elif self.position == 0:
                    signal = SignalType.SELL
                    volume = 1.0
                    # 更新持仓状态
                    self.position = -1
                    self.position_bars = 0
                    self.entry_time = klines.index[-1] if isinstance(
                        klines.index, pd.DatetimeIndex) else len(klines) - 1

        return {
            'signal': signal,
            'volume': volume,
            'price': klines['close'].iloc[-1],
            'position': self.position,
            'bars_held': self.position_bars
        }


# 使用示例
if __name__ == "__main__":
    """
    示例运行脚本

    创建模拟K线数据并执行均线交叉策略，展示策略回测结果

    输入:
        无直接输入参数，使用内部生成的随机K线数据

    输出:
        打印最后5条交易结果，包含日期、收盘价、信号类型、交易量、持仓状态和持仓K线数
    """
    # 创建模拟K线数据
    dates = pd.date_range(start='2023-01-01', periods=50, freq='D')
    data = {
        'open': np.random.normal(100, 5, 50),  # 开盘价，均值100，标准差5
        'high': np.random.normal(105, 5, 50),  # 最高价，均值105，标准差5
        'low': np.random.normal(95, 5, 50),    # 最低价，均值95，标准差5
        'close': np.random.normal(100, 5, 50),  # 收盘价，均值100，标准差5
        'volume': np.random.normal(1000, 200, 50)  # 成交量，均值1000，标准差200
    }
    klines = pd.DataFrame(data, index=dates)

    # 创建策略实例
    strategy = MAStrategy(fast_period=10, slow_period=20)

    # 模拟策略执行
    results = []
    for i in range(strategy.window_size, len(klines)):
        # 获取当前窗口的K线数据
        window = klines.iloc[i-strategy.window_size:i+1]
        # 执行策略
        result = strategy.execute(window)
        # 记录结果
        results.append({
            'date': window.index[-1],          # 当前K线日期
            'close': window['close'].iloc[-1],  # 当前收盘价
            'signal': result['signal'],        # 交易信号
            'volume': result['volume'],        # 交易量
            'position': result['position'],    # 持仓状态
            'bars_held': result['bars_held']   # 持仓K线数
        })

    # 转换结果为DataFrame
    # 参数: results (list): 包含每个时间点策略执行结果的字典列表
    # 返回: pandas.DataFrame: 包含所有回测结果的数据框
    results_df = pd.DataFrame(results)

    # 打印最后5条结果
    # 使用tail()方法获取DataFrame的最后5行
    print(results_df.tail())
