"""
MA交叉移动止损策略 v0.1

这是一个基于移动平均线交叉的交易策略，具有移动止损功能。

主要特性：
1. 均线交叉信号：短期均线上穿长期均线时做多，下穿时做空
2. 移动止损：设置固定百分比止损线
3. 移动止盈：价格突破止盈线后跟踪最高/最低价，回撤到指定比例时止盈
4. 实时信号生成：支持逐根K线处理，适合实时交易系统

使用方法：
1. 创建策略实例：strategy = MaCrossTrailingStopStrategy()
2. 调用calculate_signal(df)计算信号，返回包含信号信息的字典
3. 信号值：1=做多，-1=做空，0=无信号/平仓

信号字典格式：
{
    'signal': int,  # 1=做多, -1=做空, 0=无信号/平仓
    'signal_type': str,  # 信号类型
    'current_position': str,  # 当前持仓方向
    'price': float,  # 当前价格
    'short_ma': float,  # 短期均线值
    'long_ma': float,  # 长期均线值
    'datetime': datetime,  # 时间
    'reason': str  # 信号原因描述
}
"""

import pandas as pd
import numpy as np
import logging
from typing import Tuple, Optional, Dict, Any
from dataclasses import dataclass
from datetime import datetime


@dataclass
class Position:
    """持仓信息"""
    direction: str  # "long" 或 "short"
    price: float  # 开仓价格
    volume: float  # 持仓数量
    datetime: datetime  # 开仓时间
    stop_loss_price: float = 0.0  # 止损价格
    take_profit_price: float = 0.0  # 止盈价格
    extreme_price: float = 0.0  # 极值价格：多头记录最高价，空头记录最低价


class MaCrossTrailingStopStrategy:
    """
    MA交叉策略（带移动止损）- 实时版本

    策略逻辑：
    1. 当短期均线上穿长期均线时产生做多信号
    2. 当短期均线下穿长期均线时产生做空信号
    3. 每次开仓后设置止损和止盈线
    4. 实现移动止盈机制：价格突破止盈线后跟踪最高/最低价，回撤到指定比例时止盈

    实时数据流集成：
    - 与online_data_service.Dataservice集成
    - 支持实时K线数据更新
    - 每次新K线闭合时自动计算信号
    """

    def __init__(self,
                 data_service=None,
                 short_window: int = 5,
                 long_window: int = 100,
                 stop_loss_pct: float = 2.0,
                 profit_take_ratio: float = 0.8,
                 logger=None):
        """
        初始化策略参数

        参数:
        data_service: Dataservice实例 - 数据服务对象
        short_window: int - 短期均线周期 (默认5)
        long_window: int - 长期均线周期 (默认100)
        stop_loss_pct: float - 止损百分比 (默认2.0%)
        profit_take_ratio: float - 回撤止盈比例 (默认0.8，即80%)
        logger: logging.Logger - 日志记录器 (可选)
        """
        self.data_service = data_service
        self.short_window = short_window
        self.long_window = long_window
        self.stop_loss_pct = stop_loss_pct
        self.profit_take_ratio = profit_take_ratio

        # 设置日志记录器
        self.logger = logger or logging.getLogger(__name__)

        # 均线数据
        self.short_ma: Optional[pd.Series] = None
        self.long_ma: Optional[pd.Series] = None

        # 历史均线值，用于判断交叉
        self.last_short_ma: float = 0.0
        self.last_long_ma: float = 0.0

        # 当前持仓
        self.positions: list[Position] = []

        # 信号历史
        self.signal_history: list[Dict[str, Any]] = []

        # 回调函数
        self.on_signal_callback = None

    def set_data_service(self, data_service):
        """设置数据服务"""
        self.data_service = data_service

    def set_signal_callback(self, callback_func):
        """设置信号回调函数"""
        self.on_signal_callback = callback_func

    def on_kline_update(self, kline_msg: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        处理实时K线更新

        参数:
        kline_msg: Dict[str, Any] - websocket接收到的K线消息

        返回:
        Optional[Dict[str, Any]] - 如果产生信号则返回信号字典，否则返回None
        """
        if not self.data_service:
            self.logger.warning("警告：数据服务未设置")
            return None

        # 检查是否为闭合的K线
        if not kline_msg.get('k', {}).get('x', False):
            return None  # 只处理闭合的K线

        try:
            # 更新数据服务中的K线数据
            self.data_service._update_kline(kline_msg)

            # 获取最新的K线数据
            kline_df = self.data_service.get_kline()

            # 转换为策略需要的格式
            strategy_df = self._convert_kline_format(kline_df)

            # 计算信号
            signal = self.calculate_signal_realtime(strategy_df)

            # 如果有信号回调函数，则调用
            if self.on_signal_callback and signal:
                self.on_signal_callback(signal)

            return signal

        except Exception as e:
            self.logger.error(f"处理K线更新时出错: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return None

    def _convert_kline_format(self, kline_df: pd.DataFrame) -> pd.DataFrame:
        """
        将Dataservice的K线格式转换为策略需要的格式

        参数:
        kline_df: pd.DataFrame - Dataservice的K线数据

        返回:
        pd.DataFrame - 策略格式的K线数据
        """
        # 创建策略需要的DataFrame格式
        strategy_df = pd.DataFrame({
            'open': kline_df['open'],
            'high': kline_df['high'],
            'low': kline_df['low'],
            'close': kline_df['close'],
            'volume': kline_df['volume']
        })

        # 设置时间索引
        strategy_df.index = pd.to_datetime(kline_df['open_time'], unit='ms')

        return strategy_df

    def calculate_signal_realtime(self, data: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """
        实时计算交易信号（优化版本）

        参数:
        data: pd.DataFrame - K线数据

        返回:
        Optional[Dict[str, Any]] - 信号字典，如果无重要信号则返回None
        """
        # 验证数据
        if len(data) < self.long_window:
            return None  # 数据不足，不产生信号

        # 计算均线
        self.calculate_moving_averages(data)

        # 获取最新的均线值
        current_short_ma = self.short_ma.iloc[-1]
        current_long_ma = self.long_ma.iloc[-1]
        current_price = data['close'].iloc[-1]
        current_datetime = data.index[-1]

        # 构建当前K线数据
        current_bar = {
            'open': data['open'].iloc[-1],
            'high': data['high'].iloc[-1],
            'low': data['low'].iloc[-1],
            'close': current_price,
            'volume': data['volume'].iloc[-1]
        }

        # 检查止损止盈条件
        positions_to_close = self.check_stop_conditions(current_bar)

        # 处理需要平仓的持仓
        for position, reason in positions_to_close:
            self.positions.remove(position)
            signal_info = {
                'signal': 0,  # 平仓信号
                'signal_type': 'stop_loss' if reason == '止损' else 'take_profit',
                'current_position': 'none',
                'price': current_price,
                'short_ma': current_short_ma,
                'long_ma': current_long_ma,
                'datetime': current_datetime,
                'reason': f'{position.direction}仓位{reason}平仓，开仓价:{position.price:.4f}，平仓价:{current_price:.4f}',
                'action': 'close_position'
            }
            self.signal_history.append(signal_info)

            # 更新历史均线值
            self.last_short_ma = current_short_ma
            self.last_long_ma = current_long_ma

            # 不在这里打印，由回调函数处理
            return signal_info

        # 检测交叉信号
        cross_signal = self.detect_cross_signal(
            current_short_ma, current_long_ma)

        # 获取当前持仓状态
        current_position_direction = self._get_current_position_direction()

        # 处理交叉信号
        signal_result = None
        if cross_signal == "golden_cross" and current_position_direction != "long":
            signal_result = self._handle_golden_cross(
                current_price, current_short_ma, current_long_ma, current_datetime, current_position_direction)
        elif cross_signal == "death_cross" and current_position_direction != "short":
            signal_result = self._handle_death_cross(
                current_price, current_short_ma, current_long_ma, current_datetime, current_position_direction)

        # 更新历史均线值（无论是否有信号都要更新）
        self.last_short_ma = current_short_ma
        self.last_long_ma = current_long_ma

        # 返回信号结果
        return signal_result

    def _get_current_position_direction(self) -> str:
        """获取当前持仓方向"""
        if not self.positions:
            return 'none'

        long_positions = [p for p in self.positions if p.direction == 'long']
        short_positions = [p for p in self.positions if p.direction == 'short']

        if long_positions:
            return 'long'
        elif short_positions:
            return 'short'
        else:
            return 'none'

    def _handle_golden_cross(self, current_price: float, current_short_ma: float,
                             current_long_ma: float, current_datetime, current_position_direction: str) -> Dict[str, Any]:
        """处理金叉信号"""
        # 如果当前持有空仓，先平仓
        if current_position_direction == "short":
            self.positions = [
                p for p in self.positions if p.direction != 'short']

        # 开多仓
        new_position = Position(
            direction="long",
            price=current_price,
            volume=1.0,
            datetime=current_datetime,
            stop_loss_price=current_price * (1 - self.stop_loss_pct / 100),
            take_profit_price=current_price * (1 + self.stop_loss_pct / 100),
            extreme_price=current_price
        )
        self.positions.append(new_position)

        signal_info = {
            'signal': 1,
            'signal_type': 'golden_cross',
            'current_position': 'long',
            'price': current_price,
            'short_ma': current_short_ma,
            'long_ma': current_long_ma,
            'datetime': current_datetime,
            'reason': f'金叉信号：短线({current_short_ma:.4f})上穿长线({current_long_ma:.4f})，开多仓',
            'action': 'open_long'
        }

        self.signal_history.append(signal_info)
        # 不在这里打印，由回调函数处理
        return signal_info

    def _handle_death_cross(self, current_price: float, current_short_ma: float,
                            current_long_ma: float, current_datetime, current_position_direction: str) -> Dict[str, Any]:
        """处理死叉信号"""
        # 如果当前持有多仓，先平仓
        if current_position_direction == "long":
            self.positions = [
                p for p in self.positions if p.direction != 'long']

        # 开空仓
        new_position = Position(
            direction="short",
            price=current_price,
            volume=1.0,
            datetime=current_datetime,
            stop_loss_price=current_price * (1 + self.stop_loss_pct / 100),
            take_profit_price=current_price * (1 - self.stop_loss_pct / 100),
            extreme_price=current_price  # 空头初始化为开仓价，后续会在check_stop_conditions中正确更新
        )
        self.positions.append(new_position)

        signal_info = {
            'signal': -1,
            'signal_type': 'death_cross',
            'current_position': 'short',
            'price': current_price,
            'short_ma': current_short_ma,
            'long_ma': current_long_ma,
            'datetime': current_datetime,
            'reason': f'死叉信号：短线({current_short_ma:.4f})下穿长线({current_long_ma:.4f})，开空仓',
            'action': 'open_short'
        }

        self.signal_history.append(signal_info)
        # 不在这里打印，由回调函数处理
        return signal_info

    def calculate_moving_averages(self, data: pd.DataFrame) -> Tuple[pd.Series, pd.Series]:
        """
        计算短期和长期移动平均线

        参数:
        data: pd.DataFrame - 必须包含'close'列的K线数据

        返回:
        Tuple[pd.Series, pd.Series] - (短期均线序列, 长期均线序列)
        """
        if not isinstance(data, pd.DataFrame):
            raise ValueError("输入必须是pandas DataFrame")

        if 'close' not in data.columns:
            raise ValueError("数据必须包含'close'列")

        if len(data) < self.long_window:
            raise ValueError(f"数据长度应≥{self.long_window}以保证长期均线计算的可靠性")

        # 计算移动平均 (使用收盘价)
        self.short_ma = data['close'].rolling(window=self.short_window).mean()
        self.long_ma = data['close'].rolling(window=self.long_window).mean()

        return self.short_ma, self.long_ma

    def detect_cross_signal(self, current_short_ma: float, current_long_ma: float) -> Optional[str]:
        """
        检测均线交叉信号

        参数:
        current_short_ma: float - 当前短期均线值
        current_long_ma: float - 当前长期均线值

        返回:
        Optional[str] - "golden_cross"(金叉), "death_cross"(死叉), 或 None
        """
        # 如果没有历史数据，无法判断交叉
        if self.last_short_ma == 0.0 or self.last_long_ma == 0.0:
            return None

        # 添加阈值避免浮点数精度问题
        threshold = 0.0001

        # 金叉：上一根K线短线在长线下方，当前K线短线在长线上方
        if (self.last_short_ma < self.last_long_ma and
            current_short_ma > current_long_ma and
                abs(current_short_ma - current_long_ma) > threshold):
            return "golden_cross"

        # 死叉：上一根K线短线在长线上方，当前K线短线在长线下方
        if (self.last_short_ma > self.last_long_ma and
            current_short_ma < current_long_ma and
                abs(current_short_ma - current_long_ma) > threshold):
            return "death_cross"

        return None

    def check_stop_conditions(self, bar_data: Dict[str, float]) -> list[Tuple[Position, str]]:
        """
        检查止损和移动止盈条件

        参数:
        bar_data: Dict[str, float] - 当前K线数据，包含open, high, low, close

        返回:
        list[Tuple[Position, str]] - 需要平仓的持仓列表，每个元素为(持仓对象, 平仓原因)
        """
        positions_to_close = []
        current_price = bar_data['close']
        high_price = bar_data['high']
        low_price = bar_data['low']

        for position in self.positions:
            if position.direction == "long":
                # 多头持仓处理

                # 更新最高价并重新设置止盈价格
                if high_price > position.extreme_price:
                    position.extreme_price = high_price
                    # 更新止盈价格 (开仓价格 + 止损百分比)
                    position.take_profit_price = position.price * \
                        (1 + self.stop_loss_pct / 100)

                # 检查止损
                stop_loss_price = position.price * \
                    (1 - self.stop_loss_pct / 100)
                if current_price <= stop_loss_price:
                    positions_to_close.append((position, "止损"))
                    continue

                # 检查移动止盈
                # 只有当最高价曾经超过止盈线，且当前回撤到指定比例时才触发
                if position.extreme_price > position.take_profit_price:
                    # 计算回撤价格
                    retrace_price = position.take_profit_price + \
                        (position.extreme_price - position.take_profit_price) * \
                        (1 - self.profit_take_ratio)
                    if current_price <= retrace_price:
                        positions_to_close.append((position, "移动止盈"))
                        continue

            else:  # direction == "short"
                # 空头持仓处理

                # 空头持仓，更新最低价
                if low_price < position.extreme_price:
                    position.extreme_price = low_price
                    # 更新止盈价格 (开仓价格 - 止损百分比) - 保持为原始固定值
                    position.take_profit_price = position.price * \
                        (1 - self.stop_loss_pct / 100)

                # 检查止损
                stop_loss_price = position.price * \
                    (1 + self.stop_loss_pct / 100)
                if current_price >= stop_loss_price:
                    positions_to_close.append((position, "止损"))
                    continue

                # 检查移动止盈
                # 只有当最低价曾经低于止盈线，且当前反弹到指定比例时才触发
                if position.extreme_price < position.take_profit_price:
                    # 计算回撤价格
                    retrace_price = position.take_profit_price - \
                        (position.take_profit_price - position.extreme_price) * \
                        (1 - self.profit_take_ratio)
                    if current_price >= retrace_price:
                        positions_to_close.append((position, "移动止盈"))
                        continue

        return positions_to_close

    def calculate_signal(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        计算交易信号

        参数:
        data: pd.DataFrame - K线数据，必须包含open, high, low, close, volume列

        返回:
        Dict[str, Any] - 包含信号信息的字典
        {
            'signal': int,  # 1=做多, -1=做空, 0=无信号/平仓
            'signal_type': str,  # 'golden_cross', 'death_cross', 'stop_loss', 'take_profit', 'hold'
            'current_position': str,  # 'long', 'short', 'none'
            'price': float,  # 当前价格
            'short_ma': float,  # 短期均线值
            'long_ma': float,  # 长期均线值
            'datetime': datetime,  # 当前时间
            'reason': str  # 信号产生原因的详细描述
        }
        """
        # 验证数据
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [
            col for col in required_columns if col not in data.columns]
        if missing_columns:
            raise ValueError(f"数据缺少必要的列: {missing_columns}")

        if len(data) < self.long_window:
            return {
                'signal': 0,
                'signal_type': 'insufficient_data',
                'current_position': 'none',
                'price': data['close'].iloc[-1] if len(data) > 0 else 0.0,
                'short_ma': 0.0,
                'long_ma': 0.0,
                'datetime': data.index[-1] if len(data) > 0 else datetime.now(),
                'reason': f'数据不足，需要至少{self.long_window}根K线'
            }

        # 计算均线
        self.calculate_moving_averages(data)

        # 获取最新的均线值
        current_short_ma = self.short_ma.iloc[-1]
        current_long_ma = self.long_ma.iloc[-1]
        current_price = data['close'].iloc[-1]
        current_datetime = data.index[-1]

        # 构建当前K线数据
        current_bar = {
            'open': data['open'].iloc[-1],
            'high': data['high'].iloc[-1],
            'low': data['low'].iloc[-1],
            'close': current_price,
            'volume': data['volume'].iloc[-1]
        }

        # 检查止损止盈条件
        positions_to_close = self.check_stop_conditions(current_bar)

        # 处理需要平仓的持仓
        for position, reason in positions_to_close:
            self.positions.remove(position)
            signal_info = {
                'signal': 0,  # 平仓信号
                'signal_type': 'stop_loss' if reason == '止损' else 'take_profit',
                'current_position': 'none',
                'price': current_price,
                'short_ma': current_short_ma,
                'long_ma': current_long_ma,
                'datetime': current_datetime,
                'reason': f'{position.direction}仓位{reason}平仓，开仓价:{position.price:.4f}，平仓价:{current_price:.4f}'
            }
            self.signal_history.append(signal_info)

            # 更新历史均线值
            self.last_short_ma = current_short_ma
            self.last_long_ma = current_long_ma

            return signal_info

        # 检测交叉信号
        cross_signal = self.detect_cross_signal(
            current_short_ma, current_long_ma)

        # 获取当前持仓状态
        current_position_direction = 'none'
        if self.positions:
            # 假设只有一个方向的持仓
            long_positions = [
                p for p in self.positions if p.direction == 'long']
            short_positions = [
                p for p in self.positions if p.direction == 'short']

            if long_positions:
                current_position_direction = 'long'
            elif short_positions:
                current_position_direction = 'short'

        signal_info = {
            'signal': 0,
            'signal_type': 'hold',
            'current_position': current_position_direction,
            'price': current_price,
            'short_ma': current_short_ma,
            'long_ma': current_long_ma,
            'datetime': current_datetime,
            'reason': '无交易信号，持有当前仓位'
        }

        # 处理交叉信号
        if cross_signal == "golden_cross":
            # 金叉信号 - 做多
            if current_position_direction != "long":
                # 如果当前持有空仓，先平仓
                if current_position_direction == "short":
                    self.positions = [
                        p for p in self.positions if p.direction != 'short']

                # 开多仓
                new_position = Position(
                    direction="long",
                    price=current_price,
                    volume=1.0,  # 这里可以根据需要调整
                    datetime=current_datetime,
                    stop_loss_price=current_price *
                    (1 - self.stop_loss_pct / 100),
                    take_profit_price=current_price *
                    (1 + self.stop_loss_pct / 100),
                    extreme_price=current_price
                )
                self.positions.append(new_position)

                signal_info.update({
                    'signal': 1,
                    'signal_type': 'golden_cross',
                    'current_position': 'long',
                    'reason': f'金叉信号：短线({current_short_ma:.4f})上穿长线({current_long_ma:.4f})，开多仓'
                })

        elif cross_signal == "death_cross":
            # 死叉信号 - 做空
            if current_position_direction != "short":
                # 如果当前持有多仓，先平仓
                if current_position_direction == "long":
                    self.positions = [
                        p for p in self.positions if p.direction != 'long']

                # 开空仓
                new_position = Position(
                    direction="short",
                    price=current_price,
                    volume=1.0,  # 这里可以根据需要调整
                    datetime=current_datetime,
                    stop_loss_price=current_price *
                    (1 + self.stop_loss_pct / 100),
                    take_profit_price=current_price *
                    (1 - self.stop_loss_pct / 100),
                    extreme_price=current_price
                )
                self.positions.append(new_position)

                signal_info.update({
                    'signal': -1,
                    'signal_type': 'death_cross',
                    'current_position': 'short',
                    'reason': f'死叉信号：短线({current_short_ma:.4f})下穿长线({current_long_ma:.4f})，开空仓'
                })

        # 更新历史均线值
        self.last_short_ma = current_short_ma
        self.last_long_ma = current_long_ma

        # 记录信号历史
        self.signal_history.append(signal_info)

        return signal_info

    def process_new_bar(self, new_bar_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理新的K线数据（增量更新）

        参数:
        new_bar_data: Dict[str, Any] - 新的K线数据
        {
            'datetime': datetime,
            'open': float,
            'high': float,
            'low': float,
            'close': float,
            'volume': float
        }

        返回:
        Dict[str, Any] - 信号信息
        """
        # 将新K线数据转换为DataFrame格式
        new_df = pd.DataFrame([new_bar_data])
        new_df.set_index('datetime', inplace=True)

        # 如果是第一根K线，直接使用
        if self.short_ma is None or self.long_ma is None:
            # 需要足够的历史数据才能计算信号
            return {
                'signal': 0,
                'signal_type': 'insufficient_data',
                'current_position': 'none',
                'price': new_bar_data['close'],
                'short_ma': 0.0,
                'long_ma': 0.0,
                'datetime': new_bar_data['datetime'],
                'reason': '首次接收数据，需要更多历史数据'
            }

        # 这里假设我们有足够的历史数据来计算均线
        # 在实际应用中，你可能需要维护一个滑动窗口的历史数据
        return self.calculate_signal(new_df)

    def get_current_position_info(self) -> Dict[str, Any]:
        """
        获取当前持仓信息

        返回:
        Dict[str, Any] - 持仓信息
        """
        if not self.positions:
            return {
                'direction': 'none',
                'total_volume': 0.0,
                'avg_price': 0.0,
                'unrealized_pnl': 0.0,
                'positions_count': 0
            }

        long_positions = [p for p in self.positions if p.direction == 'long']
        short_positions = [p for p in self.positions if p.direction == 'short']

        if long_positions:
            total_volume = sum(p.volume for p in long_positions)
            avg_price = sum(
                p.price * p.volume for p in long_positions) / total_volume
            return {
                'direction': 'long',
                'total_volume': total_volume,
                'avg_price': avg_price,
                'positions_count': len(long_positions),
                'positions': long_positions
            }
        elif short_positions:
            total_volume = sum(p.volume for p in short_positions)
            avg_price = sum(
                p.price * p.volume for p in short_positions) / total_volume
            return {
                'direction': 'short',
                'total_volume': total_volume,
                'avg_price': avg_price,
                'positions_count': len(short_positions),
                'positions': short_positions
            }

        return {
            'direction': 'none',
            'total_volume': 0.0,
            'avg_price': 0.0,
            'positions_count': 0
        }

    def reset(self):
        """重置策略状态"""
        self.short_ma = None
        self.long_ma = None
        self.last_short_ma = 0.0
        self.last_long_ma = 0.0
        self.positions = []
        self.signal_history = []


# 使用示例
if __name__ == "__main__":
    # 创建策略实例
    strategy = MaCrossTrailingStopStrategy(
        short_window=5,
        long_window=100,
        stop_loss_pct=2.0,
        profit_take_ratio=0.8
    )

    # 示例：模拟K线数据
    import numpy as np

    # 生成模拟数据
    np.random.seed(42)
    dates = pd.date_range('2024-01-01', periods=200, freq='1H')

    # 生成价格数据（随机游走 + 趋势）
    base_price = 100.0
    price_changes = np.random.normal(0, 0.02, 200)
    trend = np.linspace(0, 0.3, 200)  # 30%的上升趋势

    prices = []
    current_price = base_price
    for i in range(200):
        current_price = current_price * (1 + price_changes[i] + trend[i]/200)
        prices.append(current_price)

    # 创建OHLCV数据
    data = []
    for i, (date, price) in enumerate(zip(dates, prices)):
        # 简单模拟OHLC
        noise = np.random.normal(0, 0.005, 4)
        open_price = price * (1 + noise[0])
        high_price = price * (1 + abs(noise[1]))
        low_price = price * (1 - abs(noise[2]))
        close_price = price * (1 + noise[3])
        volume = np.random.uniform(1000, 5000)

        data.append({
            'datetime': date,
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price,
            'volume': volume
        })

    df = pd.DataFrame(data)
    df.set_index('datetime', inplace=True)

    print("=== MA交叉移动止损策略测试 ===")
    print(f"策略参数：短线MA{strategy.short_window}, 长线MA{strategy.long_window}")
    print(
        f"止损比例：{strategy.stop_loss_pct}%, 回撤止盈比例：{strategy.profit_take_ratio*100}%")
    print(f"数据范围：{df.index[0]} 到 {df.index[-1]}")
    print(f"总K线数：{len(df)}")
    print()

    # 方式1：一次性计算所有信号
    print("=== 方式1：批量计算信号 ===")
    try:
        signal = strategy.calculate_signal(df)
        print(f"最终信号：{signal}")
        print(f"信号历史数量：{len(strategy.signal_history)}")

        # 显示所有交易信号
        print("\n交易信号历史：")
        for i, sig in enumerate(strategy.signal_history):
            if sig['signal'] != 0 or sig['signal_type'] in ['stop_loss', 'take_profit']:
                print(
                    f"{i+1}. {sig['datetime']}: {sig['signal_type']} - {sig['reason']}")

        print(f"\n当前持仓：{strategy.get_current_position_info()}")

    except Exception as e:
        print(f"批量计算出错：{e}")

    print("\n" + "="*50)

    # 方式2：逐根K线处理（模拟实时）
    print("=== 方式2：逐根K线处理（模拟实时） ===")
    strategy.reset()  # 重置策略状态

    signals = []
    for i in range(len(df)):
        # 获取到当前为止的所有数据
        current_data = df.iloc[:i+1]

        if len(current_data) >= strategy.long_window:
            try:
                signal = strategy.calculate_signal(current_data)
                if signal['signal'] != 0 or signal['signal_type'] in ['stop_loss', 'take_profit']:
                    signals.append(signal)
                    print(
                        f"{signal['datetime']}: {signal['signal_type']} - 信号:{signal['signal']} - {signal['reason']}")
            except Exception as e:
                print(f"第{i+1}根K线处理出错：{e}")

    print(f"\n实时处理产生的交易信号数量：{len(signals)}")
    print(f"最终持仓：{strategy.get_current_position_info()}")

    # 方式3：使用process_new_bar方法（需要维护历史数据窗口）
    print("\n=== 方式3：使用process_new_bar方法 ===")
    print("注意：此方法需要外部维护历史数据窗口，这里仅作演示")

    strategy.reset()

    # 模拟处理最后几根K线
    for i in range(max(0, len(df)-5), len(df)):
        bar_data = {
            'datetime': df.index[i],
            'open': df['open'].iloc[i],
            'high': df['high'].iloc[i],
            'low': df['low'].iloc[i],
            'close': df['close'].iloc[i],
            'volume': df['volume'].iloc[i]
        }

        signal = strategy.process_new_bar(bar_data)
        if signal['signal_type'] != 'insufficient_data':
            print(
                f"{bar_data['datetime']}: {signal['signal_type']} - 信号:{signal['signal']}")

    print("\n=== 测试完成 ===")
