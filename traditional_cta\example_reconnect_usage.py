#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket重连功能使用示例
演示如何配置和使用WebSocket重连功能
"""

import time
import logging
from datetime import datetime
from pathlib import Path

# 示例：自定义重连配置
CUSTOM_RECONNECT_CONFIG = {
    "max_retries": 5,                   # 最大重试5次
    "initial_delay": 3,                 # 初始延迟3秒
    "max_delay": 30,                    # 最大延迟30秒
    "backoff_factor": 1.5,              # 退避因子1.5
    "ping_interval": 20,                # 心跳间隔20秒
    "ping_timeout": 8,                  # 心跳超时8秒
    "enable_ping": True                 # 启用心跳检测
}

# 示例：保守的重连配置（适用于网络不稳定的环境）
CONSERVATIVE_RECONNECT_CONFIG = {
    "max_retries": 15,                  # 更多重试次数
    "initial_delay": 5,                 # 较长的初始延迟
    "max_delay": 120,                   # 较长的最大延迟
    "backoff_factor": 1.8,              # 较大的退避因子
    "ping_interval": 45,                # 较长的心跳间隔
    "ping_timeout": 15,                 # 较长的心跳超时
    "enable_ping": True                 # 启用心跳检测
}

# 示例：激进的重连配置（适用于网络稳定的环境）
AGGRESSIVE_RECONNECT_CONFIG = {
    "max_retries": 3,                   # 较少重试次数
    "initial_delay": 1,                 # 较短的初始延迟
    "max_delay": 15,                    # 较短的最大延迟
    "backoff_factor": 2.0,              # 标准退避因子
    "ping_interval": 15,                # 较短的心跳间隔
    "ping_timeout": 5,                  # 较短的心跳超时
    "enable_ping": True                 # 启用心跳检测
}

# 示例：无心跳检测配置（适用于不支持心跳的环境）
NO_PING_RECONNECT_CONFIG = {
    "max_retries": 10,                  # 标准重试次数
    "initial_delay": 2,                 # 标准初始延迟
    "max_delay": 60,                    # 标准最大延迟
    "backoff_factor": 2.0,              # 标准退避因子
    "ping_interval": 30,                # 心跳间隔（不会使用）
    "ping_timeout": 10,                 # 心跳超时（不会使用）
    "enable_ping": False                # 禁用心跳检测
}

def demonstrate_reconnect_delays(config, config_name):
    """演示重连延迟计算"""
    print(f"\n📊 {config_name} 重连延迟演示:")
    print("-" * 40)
    
    for retry_count in range(1, min(8, config["max_retries"] + 1)):
        delay = min(
            config["initial_delay"] * 
            (config["backoff_factor"] ** (retry_count - 1)),
            config["max_delay"]
        )
        print(f"第 {retry_count} 次重连: {delay:.2f} 秒后")

def show_config_comparison():
    """显示不同配置的对比"""
    print("🔧 WebSocket重连配置对比")
    print("=" * 60)
    
    configs = [
        (CUSTOM_RECONNECT_CONFIG, "自定义配置"),
        (CONSERVATIVE_RECONNECT_CONFIG, "保守配置"),
        (AGGRESSIVE_RECONNECT_CONFIG, "激进配置"),
        (NO_PING_RECONNECT_CONFIG, "无心跳配置")
    ]
    
    for config, name in configs:
        demonstrate_reconnect_delays(config, name)

def show_usage_examples():
    """显示使用示例"""
    print("\n💡 使用示例")
    print("=" * 60)
    
    print("\n1. 修改 websocket_binance.py 中的配置:")
    print("```python")
    print("# 将 RECONNECT_CONFIG 替换为以下配置之一")
    print("RECONNECT_CONFIG = {")
    print('    "max_retries": 10,')
    print('    "initial_delay": 1,')
    print('    "max_delay": 60,')
    print('    "backoff_factor": 2,')
    print('    "ping_interval": 30,')
    print('    "ping_timeout": 10,')
    print('    "enable_ping": True')
    print("}")
    print("```")
    
    print("\n2. 根据网络环境选择配置:")
    print("   - 网络稳定: 使用激进配置")
    print("   - 网络不稳定: 使用保守配置")
    print("   - 不支持心跳: 使用无心跳配置")
    print("   - 自定义需求: 使用自定义配置")
    
    print("\n3. 监控重连状态:")
    print("   - 查看日志文件: logs/trading_system_YYYYMMDD.log")
    print("   - 关注重连相关日志信息")
    print("   - 根据实际情况调整配置参数")

def show_troubleshooting_tips():
    """显示故障排除提示"""
    print("\n🔧 故障排除提示")
    print("=" * 60)
    
    tips = [
        ("连接频繁断开", [
            "检查网络稳定性",
            "增加 initial_delay 和 max_delay",
            "调整 backoff_factor 到 1.5-2.0",
            "检查代理设置"
        ]),
        ("重连失败", [
            "检查网络连接",
            "验证WebSocket URL",
            "检查防火墙设置",
            "查看详细错误日志"
        ]),
        ("心跳超时", [
            "增加 ping_timeout 值",
            "检查网络延迟",
            "考虑禁用心跳检测",
            "调整 ping_interval"
        ]),
        ("资源占用过高", [
            "减少 ping_interval",
            "禁用心跳检测",
            "增加重连延迟",
            "限制最大重试次数"
        ])
    ]
    
    for problem, solutions in tips:
        print(f"\n❓ {problem}:")
        for solution in solutions:
            print(f"   • {solution}")

def main():
    """主函数"""
    print("🚀 WebSocket重连功能使用示例")
    print("=" * 60)
    print(f"📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 显示配置对比
    show_config_comparison()
    
    # 显示使用示例
    show_usage_examples()
    
    # 显示故障排除提示
    show_troubleshooting_tips()
    
    print("\n✅ 示例演示完成!")
    print("\n📚 更多信息请参考:")
    print("   - WEBSOCKET_RECONNECT_README.md")
    print("   - websocket_binance.py 源码")
    print("   - test_reconnect.py 测试脚本")

if __name__ == "__main__":
    main()
