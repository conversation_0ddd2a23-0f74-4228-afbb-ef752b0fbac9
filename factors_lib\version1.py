import pandas as pd
import numpy as np
import functools
import warnings
warnings.simplefilter(action='ignore', category=FutureWarning)


def fill_window_800(window=800):
    '''适用于lib填充的装饰器'''
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            s = func(*args, **kwargs)
            if not isinstance(s, pd.Series):
                raise TypeError("The function must return a pandas Series")

            s_modified = s.copy()
            idx_1 = s[s == 1].index
            idx_neg1 = s[s == -1].index

            for i in idx_neg1:
                pos = s.index.get_loc(i)
                s_modified.iloc[pos:pos+window] = -1
            for i in idx_1:
                pos = s.index.get_loc(i)
                s_modified.iloc[pos:pos+window] = 1

            return s_modified
        return wrapper
    return decorator


class VersionOne:
    '''实盘demo版本1(40个初始因子加chu的21个800window填充的lib)'''
    @staticmethod
    def ret_hv_ratio_signals(d, short_period=10, long_period=30, threshold_high=1.5, threshold_low=0.5):
        c = d.close

        # 计算每日收益率的对数
        log_returns = np.log(c / c.shift(1))

        # 计算短期HV
        hv_short = log_returns.rolling(
            window=short_period).std() * np.sqrt(252)  # 年化波动率

        # 计算长期HV
        hv_long = log_returns.rolling(
            window=long_period).std() * np.sqrt(252)  # 年化波动率

        # 计算HV比率
        hv_ratio = hv_short / hv_long

        # 买卖信号
        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        for i in range(len(hv_ratio)):
            # HV比率低于低阈值时，产生买入信号
            if hv_ratio[i] < threshold_low:
                buy_signal[i] = 1  # 买入信号

            # HV比率高于高阈值时，产生卖出信号
            elif hv_ratio[i] > threshold_high:
                sell_signal[i] = -1  # 卖出信号

        # 综合信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal + sell_signal

        return signals

    @staticmethod
    def ret_td_signals(d):
        c = d.close
        td_buy_count = 0
        td_sell_count = 0
        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        # TD 买入结构
        for i in range(4, len(c)):
            # 买入结构
            if c[i] < c[i - 4]:  # 当前收盘价低于4天前收盘价
                td_buy_count += 1
            else:
                td_buy_count = 0  # 重置计数

            # 满足连续9天买入条件
            if td_buy_count == 9:
                buy_signal[i] = 1  # 产生买入信号
                td_buy_count = 0  # 重置计数器

            # 卖出结构
            if c[i] > c[i - 4]:  # 当前收盘价高于4天前收盘价
                td_sell_count += 1
            else:
                td_sell_count = 0  # 重置计数

            # 满足连续9天卖出条件
            if td_sell_count == 9:
                sell_signal[i] = -1  # 产生卖出信号
                td_sell_count = 0  # 重置计数器

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal + sell_signal

        return signals

    @staticmethod
    def ret_ao_signals(d):
        c = d.close

        # 计算AO指标
        fast_ma = c.rolling(window=5).mean()  # 快速移动平均线（5周期）
        slow_ma = c.rolling(window=34).mean()  # 慢速移动平均线（34周期）
        ao = fast_ma - slow_ma  # AO = 快速MA - 慢速MA

        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        # AO由负变正产生买入信号
        for i in range(1, len(ao)):
            if ao[i - 1] < 0 and ao[i] > 0:  # AO由负变正
                buy_signal[i] = 1  # 产生买入信号

            # AO由正变负产生卖出信号
            elif ao[i - 1] > 0 and ao[i] < 0:  # AO由正变负
                sell_signal[i] = -1  # 产生卖出信号

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal + sell_signal

        return signals

    @staticmethod
    def ret_ena_signals(d):
        c = d.close
        # 计算指数加权移动平均（EMA）
        ema_period = 14
        ema = c.ewm(span=ema_period, adjust=False).mean()

        # 归一化ENA指标
        max_ema = ema.max()  # 计算EMA的最大值
        min_ema = ema.min()  # 计算EMA的最小值
        ena = (ema - min_ema) / (max_ema - min_ema)  # 归一化ENA

        # 计算ENA的信号线（EMA的移动平均）
        ena_signal = ena.ewm(span=9, adjust=False).mean()  # 9周期的EMA作为信号线

        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        # ENA由下向上穿越信号线时产生买入信号
        for i in range(1, len(ena)):
            if ena[i - 1] < ena_signal[i - 1] and ena[i] > ena_signal[i]:  # ENA由下向上穿越信号线
                buy_signal[i] = 1  # 产生买入信号

            # ENA由上向下穿越信号线时产生卖出信号
            elif ena[i - 1] > ena_signal[i - 1] and ena[i] < ena_signal[i]:  # ENA由上向下穿越信号线
                sell_signal[i] = -1  # 产生卖出信号

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal + sell_signal

        return signals

    @staticmethod
    def ret_williams_r_sig_price(d, period=14):
        c = d.close  # 获取收盘价

        # 计算威廉指标（Williams %R）
        highest_high = d.high.rolling(window=period).max()  # 过去14天的最高价
        lowest_low = d.low.rolling(window=period).min()     # 过去14天的最低价
        williams_r = -100 * (highest_high - c) / \
            (highest_high - lowest_low)  # Williams %R 计算

        # 买入信号：威廉指标低于-80
        buy_signal = williams_r < -80

        # 卖出信号：威廉指标高于-20
        sell_signal = williams_r > -20

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal.astype(int) - sell_signal.astype(int)

        return signals  # 返回信号序列

    @staticmethod
    def ret_momentum_sig_price(d, momentum_period=14):
        c = d.close  # 获取收盘价
        # 计算动量值
        momentum = c.diff(periods=momentum_period)  # 动量值 = 当前收盘价 - 过去指定周期的收盘价

        # 买入信号：动量值大于0并上升
        golden_cross_buy = ((momentum > 0) & (
            momentum > momentum.shift(1))).astype(int)

        # 卖出信号：动量值小于0并下降
        death_cross_sell = ((momentum < 0) & (
            momentum < momentum.shift(1))).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = golden_cross_buy - death_cross_sell

        return signals  # 返回信号序列

    @staticmethod
    def ret_kc_strategy(d):
        c = d.close
        high = d.high
        low = d.low

        # 设置参数
        kc_period = 20  # 均线计算周期
        atr_period = 14  # ATR计算周期
        atr_multiplier = 2  # ATR倍数

        # 计算20日均线
        ma = c.rolling(window=kc_period).mean()

        # 计算ATR
        tr1 = high - low
        tr2 = (high - c.shift(1)).abs()
        tr3 = (low - c.shift(1)).abs()
        true_range = tr1.combine(tr2, max).combine(tr3, max)
        atr = true_range.rolling(window=atr_period).mean()

        # 计算Keltner Channel上下轨
        kc_upper = ma + (atr * atr_multiplier)  # KC上轨
        kc_lower = ma - (atr * atr_multiplier)  # KC下轨

        # 生成买入和卖出信号
        buy_signal = (c < kc_lower).astype(int)  # 当价格跌破KC下轨时买入
        sell_signal = (c > kc_upper).astype(int)  # 当价格突破KC上轨时卖出

        # 合并信号：1为买入，-1为卖出，0为中性
        signals = buy_signal - sell_signal

        return signals

    @staticmethod
    def ret_bollinger_rsi_signals(d):
        c = d.close
        low = d.low
        high = d.high
        period = 14  # 布林带和 RSI 的标准计算周期
        rsi_period = 14  # RSI 的计算周期

        # 计算 RSI
        delta = c.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        # 计算布林带
        rolling_mean = c.rolling(window=period).mean()
        rolling_std = c.rolling(window=period).std()
        lower_band = rolling_mean - (rolling_std * 2)  # 下轨
        upper_band = rolling_mean + (rolling_std * 2)  # 上轨

        # 买入信号：价格触及布林带下轨且 RSI 低于 30（超卖）
        buy_signal = ((c <= lower_band) & (rsi < 30)).astype(int)

        # 卖出信号：价格触及布林带上轨且 RSI 高于 70（超买）
        sell_signal = ((c >= upper_band) & (rsi > 70)).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal - sell_signal

        return signals

    @staticmethod
    def ret_macd_sig_price(d):
        c = d.close
        period_short = 12  # 快速EMA的周期
        period_long = 26   # 慢速EMA的周期
        signal_period = 9  # 信号线的周期

        # 计算快速和慢速EMA
        ema_short = c.ewm(span=period_short, adjust=False).mean()
        ema_long = c.ewm(span=period_long, adjust=False).mean()

        # 计算 MACD 线
        macd_line = ema_short - ema_long

        # 计算信号线
        signal_line = macd_line.ewm(span=signal_period, adjust=False).mean()

        # 买入信号：MACD 线向上穿越信号线
        golden_cross_buy = ((macd_line.shift(1) < signal_line.shift(1)) & (
            macd_line >= signal_line)).astype(int)

        # 卖出信号：MACD 线向下穿越信号线
        death_cross_sell = ((macd_line.shift(1) > signal_line.shift(1)) & (
            macd_line <= signal_line)).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = golden_cross_buy - death_cross_sell

        return signals

    @staticmethod
    def ret_ma_arrangement_sig(d):
        close = d.close  # 收盘价
        low = d.low      # K线最低价

        # 计算均线
        ma5 = close.rolling(window=5).mean()
        ma10 = close.rolling(window=10).mean()
        ma20 = close.rolling(window=20).mean()

        # 买入信号：第一次出现币价 > MA5 > MA10 > MA20
        buy_signal = ((low > ma5) & (ma5 > ma10) & (ma10 > ma20) &
                      (~((low.shift(1) > ma5.shift(1)) & (ma5.shift(1) > ma10.shift(1)) & (ma10.shift(1) > ma20.shift(1))))).astype(int)

        # 卖出信号：第一次出现币价 < MA5 < MA10 < MA20
        sell_signal = ((low < ma5) & (ma5 < ma10) & (ma10 < ma20) &
                       (~((low.shift(1) < ma5.shift(1)) & (ma5.shift(1) < ma10.shift(1)) & (ma10.shift(1) < ma20.shift(1))))).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal - sell_signal

        return signals

    @staticmethod
    def ret_ma20_ma120_cross_sig_price(d):

        c = d.close

        # 计算 MA20 和 MA120
        ma20 = c.rolling(window=20).mean()
        ma120 = c.rolling(window=120).mean()

        # 创建买入信号
        condu = ((c > ma120) & (c > ma20) & (c.shift(1) > ma120)
                 & (c.shift(1) <= ma20)).astype(int)

        # 创建卖出信号
        condd = ((c < ma120) & (c < ma20) & (c.shift(1) < ma120)
                 & (c.shift(1) >= ma20)).astype(int)

        s = condu - condd

        return s

    @staticmethod
    def ret_rsi_ma120_cross_sig_price(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算 RSI
        def compute_rsi(s, window=14):

            delta = c.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))

            return rsi

        rsi = compute_rsi(c)
        # 创建买入信号
        condu = ((c > ma120) & (rsi < 20) & (c > c.shift(1))).astype(int)

        # 创建卖出信号
        condd = ((c < ma120) & (rsi > 80) & (c < c.shift(1))).astype(int)

        s = condu - condd

        return s

    @staticmethod
    def ret_ma120_macd_1_cross_sig_price(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算MACD
        def compute_macd(c, short_window=12, long_window=26, signal_window=9):
            exp1 = c.ewm(span=short_window, adjust=False).mean()
            exp2 = c.ewm(span=long_window, adjust=False).mean()
            macd = exp1 - exp2
            signal = macd.ewm(span=signal_window, adjust=False).mean()
            macd_hist = macd - signal
            return macd_hist

        hist = compute_macd(c)
        # 创建买入信号
        condu = ((c > ma120) & (hist.shift(1) < 0) & (hist > 0)).astype(int)
        # 创建卖出信号
        condd = ((c < ma120) & (hist.shift(1) > 0) & (hist < 0)).astype(int)

        s = condu - condd

        return s

    @staticmethod
    def ret_ma120_bolling_cross_sig_price(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算机布林带
        def compute_bollinger_bands(c, window=20, num_std_dev=2):

            middle_band = c.rolling(window).mean()
            upper_band = middle_band + (c.rolling(window).std() * num_std_dev)
            lower_band = middle_band - (c.rolling(window).std() * num_std_dev)

            return middle_band, upper_band, lower_band

        middle_band, upper_band, lower_band = compute_bollinger_bands(c)

        # 创建买入信号
        # condu1 = ((c > ma120) & (c.shift(1) <= middle_band.shift(1)) & (c > middle_band)).astype(int)
        # 创建加仓买入信号
        condu = ((c > ma120) & (c < middle_band) & (c.shift(1) >=
                 lower_band.shift(1)) & (c > lower_band)).astype(int)

        # 创建卖出信号
        # condd1 = ((c < ma120) & (c.shift(1) >= middle_band.shift(1)) & (c < middle_band)).astype(int)
        # 创建加仓买入信号
        condd = ((c < ma120) & (c > middle_band) & (c.shift(1) <=
                 upper_band.shift(1)) & (c < upper_band)).astype(int)

        s = condu - condd

        return s

    @staticmethod
    def ret_ma120_cci_cross_sig_price(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算 CCI
        def compute_rsi(d, window=14):

            typical_price = (d.high + d.low + d.close) / 3
            sma = typical_price.rolling(window=window).mean()
            mad = (typical_price - sma).abs().rolling(window=window).mean()
            cci = (typical_price - sma) / (0.015 * mad)

            return cci

        cci_20 = compute_rsi(d, 20)

        # 创建买入信号
        condu = ((c > ma120) & (cci_20 < -100) & (c > c.shift(1))).astype(int)

        # 创建卖出信号
        condd = ((c < ma120) & (cci_20 > 100) & (c < c.shift(1))).astype(int)

        s = condu - condd

        return s

    @staticmethod
    def ret_macd_02_cross_sig_price(d):

        c = d.close

        # 计算 EMA
        def ema(series, span):
            return series.ewm(span=span, adjust=False).mean()

        # 计算 MACD
        ema12 = ema(c, 12)
        ema26 = ema(c, 26)
        macd = ema12 - ema26
        signal = ema(macd, 9)

        # 创建买入信号
        condu = ((macd > 0) & (macd > signal)).astype(int)

        # 创建卖出信号
        condd = ((macd < 0) & (macd < signal)).astype(int)

        s = condu - condd

        return s

    @staticmethod
    def ret_ma120_macd_02_cross_sig_price(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算 MACD
        def compute_macd(c):

            ema12 = c.ewm(span=12, adjust=False).mean()
            ema26 = c.ewm(span=26, adjust=False).mean()
            macd = ema12 - ema26
            signal = macd.ewm(span=9, adjust=False).mean()

            return macd, signal

        macd, signal = compute_macd(c)

        # 创建买入信号
        condu = ((c > ma120) & (macd > 0) & (macd > signal) &
                 (macd.shift(1) <= signal.shift(1))).astype(int)

        # 创建卖出信号
        condd = ((c < ma120) & (macd < 0) & (macd < signal) &
                 (macd.shift(1) >= signal.shift(1))).astype(int)

        s = condu - condd

        return s

    @staticmethod
    def ret_cci_fibonacci_signals(d):
        c = d.close
        high = d.high
        low = d.low

        # 计算CCI（Commodity Channel Index）
        period = 14
        typical_price = (high + low + c) / 3  # 典型价格
        sma = typical_price.rolling(window=period).mean()  # 典型价格的简单移动平均
        mean_deviation = (
            typical_price - sma).abs().rolling(window=period).mean()  # 平均偏差
        cci = (typical_price - sma) / (0.015 * mean_deviation)  # CCI公式

        # 计算斐波那契回撤水平
        fib_levels = [0.236, 0.382, 0.5, 0.618]  # 常见的斐波那契回撤水平
        fib_retracements = pd.DataFrame(index=c.index)

        # 计算每个周期的高低价差
        high_max = high.rolling(window=period).max()
        low_min = low.rolling(window=period).min()

        # 计算斐波那契回撤位
        fib_retracements['fib_23_6'] = high_max - \
            (high_max - low_min) * fib_levels[0]
        fib_retracements['fib_38_2'] = high_max - \
            (high_max - low_min) * fib_levels[1]
        fib_retracements['fib_50_0'] = high_max - \
            (high_max - low_min) * fib_levels[2]
        fib_retracements['fib_61_8'] = high_max - \
            (high_max - low_min) * fib_levels[3]

        # 买卖信号
        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        for i in range(1, len(c)):
            # 如果CCI大于100且当前价格接近斐波那契回撤23.6%或者38.2%
            if cci[i] > 100 and (abs(c[i] - fib_retracements['fib_23_6'][i]) < 0.02 * (high_max[i] - low_min[i]) or
                                 abs(c[i] - fib_retracements['fib_38_2'][i]) < 0.02 * (high_max[i] - low_min[i])):
                sell_signal[i] = -1  # 卖出信号

            # 如果CCI小于-100且当前价格接近斐波那契回撤61.8%或者50%
            elif cci[i] < -100 and (abs(c[i] - fib_retracements['fib_61_8'][i]) < 0.02 * (high_max[i] - low_min[i]) or
                                    abs(c[i] - fib_retracements['fib_50_0'][i]) < 0.02 * (high_max[i] - low_min[i])):
                buy_signal[i] = 1  # 买入信号

        # 综合信号：买入信号为 +1，卖出信号为 -1
        s = buy_signal + sell_signal

        return s

    @staticmethod
    def ret_ma20_volume_cross_signals(d):
        # 获取收盘价和交易量数据
        c = d.close
        volume = d.volume

        # 计算短期和长期的MA
        short_window = 50  # 短期MA窗口
        long_window = 200  # 长期MA窗口
        d['MA_short'] = c.rolling(window=short_window).mean()  # 短期MA
        d['MA_long'] = c.rolling(window=long_window).mean()  # 长期MA

        # 计算前300根K线的平均交易量
        d['avg_volume'] = volume.rolling(window=300).mean()

        # 初始化信号列，0代表没有信号
        signals = pd.Series(0, index=c.index)

        # 遍历数据生成买卖信号
        for i in range(1, len(c)):
            # 金叉条件：短期MA上穿长期MA且当前为阳线，且当前阳线收盘价的交易量大于前300根K线的平均交易量
            if d['MA_short'].iloc[i] > d['MA_long'].iloc[i] and d['MA_short'].iloc[i-1] <= d['MA_long'].iloc[i-1]:
                if c[i] > c[i-1] and volume[i] > d['avg_volume'].iloc[i]:
                    signals[i] = 1  # 买入信号

        # 死叉条件：短期MA下穿长期MA且当前为阴线，且当前阴线收盘价的交易量大于前300根K线的平均交易量
            elif d['MA_short'].iloc[i] < d['MA_long'].iloc[i] and d['MA_short'].iloc[i-1] >= d['MA_long'].iloc[i-1]:
                if c[i] < c[i-1] and volume[i] > d['avg_volume'].iloc[i]:
                    signals[i] = -1  # 卖出信号

        return signals

    @staticmethod
    def ret_ma20_rsi_macd_cross_sig_price(d):

        c = d.close

        # 计算 CCI
        def compute_rsi(d, window=14):

            typical_price = (d.high + d.low + d.close) / 3
            sma = typical_price.rolling(window=window).mean()
            mad = (typical_price - sma).abs().rolling(window=window).mean()
            cci = (typical_price - sma) / (0.015 * mad)

            return cci

        rsi = compute_rsi(d, 20)
        # cci = (c - c.rolling(window=20).mean()) / (0.015 * c.rolling(window=20).std())

        # 计算 MACD
        def compute_macd(c):

            ema12 = c.ewm(span=12, adjust=False).mean()
            ema26 = c.ewm(span=26, adjust=False).mean()
            macd = ema12 - ema26
            signal = macd.ewm(span=9, adjust=False).mean()

            return macd, signal

        macd, signal = compute_macd(c)

        # 计算 MA20 和 MA120
        ma20 = c.rolling(window=20).mean()
        ma120 = c.rolling(window=120).mean()

        # 创建买入信号
        condu = ((rsi < 20) & (ma20 > ma120) & (macd > signal)).astype(int)

        # 创建卖出信号
        condd = ((rsi > 80) & (ma20 < ma120) & (macd < signal)).astype(int)

        s = condu - condd

        return s

    @staticmethod
    def generate_bbi_factor(d):
        c = d['close']

        # BBI计算：BBI是多个周期均线的平均
        ma_3 = c.rolling(window=3).mean()   # 3周期均线
        ma_6 = c.rolling(window=6).mean()   # 6周期均线
        ma_12 = c.rolling(window=12).mean()  # 12周期均线
        ma_24 = c.rolling(window=24).mean()  # 24周期均线

        d['BBI'] = (ma_3 + ma_6 + ma_12 + ma_24) / 4  # 计算BBI线

        # 买入信号条件：价格由下向上突破BBI线
        buy_signal = (c > d['BBI']) & (c.shift(1) <= d['BBI'].shift(1))

        # 卖出信号条件：价格由上向下跌破BBI线
        sell_signal = (c < d['BBI']) & (c.shift(1) >= d['BBI'].shift(1))

        # 初始化信号：0 表示无信号
        s = pd.Series(0, index=d.index)

        # 设置买入和卖出信号
        s[buy_signal] = 1   # 买入信号
        s[sell_signal] = -1  # 卖出信号

        return s

    @staticmethod
    def ret_dc_bbi_cross_sig_price(d):
        c = d.close
        high = d.high
        low = d.low

        # 唐奇安通道参数
        dc_period = 20  # 唐奇安通道的周期
        upper = high.rolling(window=dc_period).max()
        lower = low.rolling(window=dc_period).min()
        middle = (upper + lower) / 2

        # BBI指标参数
        bbi_short = 5
        bbi_medium = 10
        bbi_long = 20
        bbi_very_long = 60

        sma_5 = c.rolling(window=bbi_short).mean()
        sma_10 = c.rolling(window=bbi_medium).mean()
        sma_20 = c.rolling(window=bbi_long).mean()
        sma_60 = c.rolling(window=bbi_very_long).mean()

        bbi = (sma_5 + sma_10 + sma_20 + sma_60) / 4

        # 生成信号
        buy_signal = ((c > middle) & (c > bbi))  # 收盘价在唐奇安通道上轨以上且高于BBI
        sell_signal = ((c < middle) & (c < bbi))  # 收盘价在唐奇安通道下轨以下且低于BBI

        # 返回买卖信号，买为1，卖为-1，其他为0
        s = buy_signal.astype(int) - sell_signal.astype(int)

        return s

    @staticmethod
    def ret_ma_cci_sig(d):
        """
        Generates trading signals based on the MA level and CCI threshold:
        - Long entry if MA is above 50 and CCI > 100.
        - Short entry if MA is below 50 and CCI < -100.
        """
        close = d.close
        high = d.high
        low = d.low

        # Parameters
        ma_period = 50  # Moving Average period for trend detection
        cci_period = 20  # CCI period (commonly used)
        cci_threshold = 100  # Threshold for CCI

        # Calculate MA
        ma = close.rolling(window=ma_period).mean()

        # Calculate CCI
        tp = (high + low + close) / 3  # Typical Price for CCI calculation
        cci = (tp - tp.rolling(window=cci_period).mean()) / \
            (0.015 * tp.rolling(window=cci_period).std())

        # Generate Signals
        long_entry = ((ma > 50) & (cci > cci_threshold)).astype(int)
        short_entry = ((ma < 50) & (cci < -cci_threshold)).astype(int)

        # Signal output: 1 for long, -1 for short
        signals = long_entry - short_entry

        return signals

    @staticmethod
    def ret_ma_vol_cci_sig(d):
        c = d.close
        vol = d.volume
        # cci = d.cci  # 假设数据集d包含CCI指标列

        def compute_rsi(d, window=14):

            typical_price = (d.high + d.low + d.close) / 3
            sma = typical_price.rolling(window=window).mean()
            mad = (typical_price - sma).abs().rolling(window=window).mean()
            cci = (typical_price - sma) / (0.015 * mad)

            return cci

        cci = compute_rsi(d)

        ma_window = 50
        vol_days_threshold = 3  # VOL放大的连续天数
        vol_increase_factor = 1.2  # 设定放大量度，比如比长期均量大20%

        # 计算50日均线
        ma_50 = c.rolling(window=ma_window).mean()

        # 计算VOL短期均量
        vol_short = vol.rolling(window=vol_days_threshold).mean()
        vol_long = vol.rolling(window=ma_window).mean()

        # 条件：价格均线位于50均线上方
        price_above_ma = (c > ma_50).astype(int)

        # 条件：VOL连续几天放大
        vol_increase = ((vol_short > vol_long * vol_increase_factor).astype(int).rolling(
            window=vol_days_threshold).sum() == vol_days_threshold).astype(int)

        # 条件：CCI指标低于-100
        cci_condition = (cci < -100).astype(int)

        # 所有条件都满足则产生买入信号
        signal = (price_above_ma & vol_increase & cci_condition).astype(int)

        return signal

    @staticmethod
    def ret_ma_atr_cross_sig_price(d):
        c = d.close
        high = d.high
        low = d.low
        long_T = 30
        short_T = 5
        atr_T = 14  # Common period for ATR calculation

        # Moving Averages
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()

        # ATR Calculation
        tr = pd.DataFrame()
        tr['h-l'] = high - low
        tr['h-c'] = (high - c.shift()).abs()
        tr['l-c'] = (low - c.shift()).abs()
        atr = tr.max(axis=1).rolling(window=atr_T).mean()

        # Signal conditions based on MA and ATR
        condu = (((ma_short > ma_long).astype(int).diff() == 1).astype(int) +
                 (atr > atr.rolling(window=long_T).mean()).astype(int)) == 2

        condd = (((ma_short < ma_long).astype(int).diff() == 1).astype(int) +
                 (atr < atr.rolling(window=long_T).mean()).astype(int)) == 2

        s = condu.astype(int) - condd.astype(int)

        return s

    @staticmethod
    def ret_dpo_ma_cross_sig_price(d):
        c = d.close
        long_T = 30  # 长期移动平均的周期
        short_T = 5  # 短期DPO的周期
        ma_T = 10  # 用来判断趋势的移动平均周期

        # 计算 DPO 值：当前价格 - 长期移动平均
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        dpo = c - ma_short  # DPO是当前价格与短期移动平均的差值

        # 计算MA值来判断趋势方向
        ma = c.rolling(window=ma_T).mean()  # 用ma_T来计算一个中期的简单移动平均

        # 买卖信号：
        # 1. DPO值为正并且价格高于MA时，认为是买入信号
        # 2. DPO值为负并且价格低于MA时，认为是卖出信号
        buy_signal = (dpo > 0) & (c > ma)
        sell_signal = (dpo < 0) & (c < ma)

        # 输出信号，买入信号为1，卖出信号为-1，其他为0
        s = buy_signal.astype(int) - sell_signal.astype(int)

        return s

    @staticmethod
    def ret_po_signals(d, short_period=9, long_period=26):
        c = d.close

        # 计算短期和长期 EMA
        ema_short = c.ewm(span=short_period, adjust=False).mean()
        ema_long = c.ewm(span=long_period, adjust=False).mean()

        # 计算 PO
        po = (ema_short - ema_long) / ema_long * 100

        # 买卖信号
        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        for i in range(1, len(po)):
            # PO 上穿 0 时，产生买入信号
            if po[i - 1] <= 0 and po[i] > 0:
                buy_signal[i] = 1  # 买入信号

            # PO 下穿 0 时，产生卖出信号
            elif po[i - 1] >= 0 and po[i] < 0:
                sell_signal[i] = -1  # 卖出信号

        # 综合信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal + sell_signal

        return signals

    @staticmethod
    def ret_rma_cross_sig_price(d):
        """
        基于 RMA 指标的多空信号策略
        :param d: DataFrame 包含 'close', 'high', 'low', 'volume' 列
        :return: 信号序列，多头为 1，空头为 -1，无信号为 0
        """
        c = d.close
        long_T = 30  # 长期周期
        short_T = 5  # 短期周期

        # 定义 RMA 计算公式
        def rma(series, window):
            return series.ewm(alpha=1/window, adjust=False).mean()

        # 计算短期和长期 RMA
        rma_short = rma(c, short_T)
        rma_long = rma(c, long_T)

        # 计算高点和低点的 RMA
        high_short = rma(d.high, short_T)
        high_long = rma(d.high, long_T)
        low_short = rma(d.low, short_T)
        low_long = rma(d.low, long_T)

        # 计算短期和长期的成交量 RMA
        vol_short = rma(d.volume, short_T)
        vol_long = rma(d.volume, long_T)

        # 多头信号条件：短期 RMA 上穿长期 RMA 且短期高点高于长期高点
        condu = ((((rma_short > rma_long).astype(int).diff() == 1).astype(int) +
                  (high_short >= high_long).astype(int)) == 2).astype(int)

        # 空头信号条件：短期 RMA 下穿长期 RMA 且短期低点低于长期低点
        condd = ((((rma_short < rma_long).astype(int).diff() == 1).astype(int) +
                  (low_short <= low_long).astype(int)) == 2).astype(int)

        # 计算成交量的比率
        ft = vol_short / vol_long

        # 返回信号（多头为 1，空头为 -1，无信号为 0）
        s = condu - condd
        return s

    @staticmethod
    def ret_ma120_bbi_signals(d):
        c = d['close']  # 收盘价格
        MA120 = c.rolling(window=120).mean()  # 120日移动平均线
        BBI = (c.rolling(window=3).mean() + c.rolling(window=5).mean() +
               c.rolling(window=8).mean()) / 3  # 假设BBI为三条移动平均的均值

        # 做多买入信号：价格大于MA120且由下向上突破BBI线
        buy_signal = ((c > MA120) & (c.shift(1) <= BBI.shift(1))
                      & (c > BBI)).astype(int)

        # 做空卖出信号：价格小于MA120且由下向下跌破BBI线
        sell_signal = ((c < MA120) & (c.shift(1) >= BBI.shift(1))
                       & (c < BBI)).astype(int)

        # 合并信号：1表示买入信号，-1表示卖出信号，0表示没有信号
        signal = buy_signal - sell_signal

        return signal

    @staticmethod
    def ret_skdj_sig_price(d):
        c = d.close
        low = d.low
        high = d.high
        period = 14  # SKDJ的标准计算周期

        # 计算SKDJ的 %K 和 %D 线
        lowest_low = low.rolling(window=period).min()
        highest_high = high.rolling(window=period).max()
        k_line = ((c - lowest_low) / (highest_high - lowest_low)) * 100
        d_line = k_line.rolling(window=3).mean()

        # 金叉买入信号：%K 上穿 %D 且都小于 25
        golden_cross_buy = ((k_line.shift(1) < d_line.shift(1)) & (
            k_line >= d_line) & (k_line < 25) & (d_line < 25)).astype(int)

        # 死叉卖出信号：%K 下穿 %D 且都大于 75
        death_cross_sell = ((k_line.shift(1) > d_line.shift(1)) & (
            k_line <= d_line) & (k_line > 75) & (d_line > 75)).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        s = golden_cross_buy - death_cross_sell

        return s

    @staticmethod
    def ret_vao_signals(d, n1=20, n2=50):
        c = d.close
        h = d.high
        l = d.low
        v = d.volume

        # 计算 WEIGHTED_VOLUME
        weighted_volume = v * (c - (h + l) / 2)

        # 计算 VAO
        vao = weighted_volume.cumsum()  # 累积加和实现 REF(VAO, 1) + WEIGHTED_VOLUME

        # 计算 VAO 的短期和长期均线
        vao_ma1 = vao.rolling(window=n1).mean()
        vao_ma2 = vao.rolling(window=n2).mean()

        # 买卖信号
        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        for i in range(1, len(vao)):
            # 短期均线上穿长期均线时，产生买入信号
            if vao_ma1[i - 1] <= vao_ma2[i - 1] and vao_ma1[i] > vao_ma2[i]:
                buy_signal[i] = 1  # 买入信号

            # 短期均线下穿长期均线时，产生卖出信号
            elif vao_ma1[i - 1] >= vao_ma2[i - 1] and vao_ma1[i] < vao_ma2[i]:
                sell_signal[i] = -1  # 卖出信号

        # 综合信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal + sell_signal

        return signals

    @staticmethod
    def ret_wma_signals(d):

        def weighted_moving_average(series, window):
            weights = range(1, window + 1)  # 权重从1到window
            return series.rolling(window=window).apply(lambda x: (x * weights).sum() / sum(weights), raw=True)

        c = d['close']  # 收盘价格
        short_T = 5  # 短期WMA窗口
        long_T = 30  # 长期WMA窗口

        # 计算短期和长期的WMA
        wma_short = weighted_moving_average(c, short_T)
        wma_long = weighted_moving_average(c, long_T)

        # 做多买入信号：短期WMA突破长期WMA
        buy_signal = ((wma_short > wma_long) & (
            wma_short.shift(1) <= wma_long.shift(1))).astype(int)

        # 做空卖出信号：短期WMA跌破长期WMA
        sell_signal = ((wma_short < wma_long) & (
            wma_short.shift(1) >= wma_long.shift(1))).astype(int)

        # 最终信号：1为买入信号，-1为卖出信号，0为无信号
        signal = buy_signal - sell_signal

        return signal

    @staticmethod
    def ret_rsi_bb_ma_signal(d):

        def calculate_rsi(series, period=14):
            delta = series.diff()
            gain = delta.where(delta > 0, 0).rolling(window=period).mean()
            loss = -delta.where(delta < 0, 0).rolling(window=period).mean()
            rs = gain / loss
            return 100 - (100 / (1 + rs))

        c = d.close
        rsi = calculate_rsi(c)
        mid_band = c.rolling(20).mean()
        upper_band = mid_band + 2 * c.rolling(20).std()
        lower_band = mid_band - 2 * c.rolling(20).std()
        ma120 = c.rolling(120).mean()

        buy_signal = ((rsi > 50) & (c > mid_band) & (
            c.shift(1) <= mid_band)).astype(int)
        add_position_signal = ((buy_signal.cumsum() > 0) & (
            c < mid_band) & (c > lower_band)).astype(int)
        sell_signal = (c >= upper_band).astype(int)

        signals = buy_signal - sell_signal + add_position_signal
        signals[ma120 > c] *= -1  # Reverse signals if below MA120
        return signals

    @staticmethod
    def ret_macd_cross_signal(d):
        c = d.close  # Get closing prices
        short_T = 12  # Short-term EMA period
        long_T = 26   # Long-term EMA period
        signal_T = 9  # Signal line period (MACD signal line)

        # Calculate short-term and long-term EMAs
        ema_short = c.ewm(span=short_T, adjust=False).mean()
        ema_long = c.ewm(span=long_T, adjust=False).mean()

        # Calculate DIF (Difference between short-term and long-term EMAs)
        dif = ema_short - ema_long

        # Calculate MACD (Signal line is an EMA of the DIF)
        macd = dif.ewm(span=signal_T, adjust=False).mean()

        # Calculate the difference between closing price and MACD for bar height
        bar = c - macd

        # Detect Golden Cross (DIF crosses above MACD)
        golden_cross = ((dif > macd) & (
            dif.shift(1) <= macd.shift(1))).astype(int)

        # Detect Death Cross (DIF crosses below MACD)
        death_cross = ((dif < macd) & (
            dif.shift(1) >= macd.shift(1))).astype(int)

        # Detect shrinking bars (current bar is smaller than the previous one)
        bar_shrink = (bar.abs() < bar.shift(1).abs()).astype(int)

        # Buy signal: Golden cross with shrinking green bars
        buy_signal = golden_cross & bar_shrink

        # Sell signal: Death cross with shrinking red bars
        sell_signal = death_cross & bar_shrink

        # Return buy and sell signals
        return buy_signal - sell_signal  # 1 for buy, -1 for sell, 0 for no action

    @staticmethod
    def ret_rsi_boll_sig(d):
        c = d.close
        high = d.high
        low = d.low

        # Parameters for Bollinger Bands and RSI
        rsi_period = 14
        boll_period = 20
        boll_std = 2

        # Calculate the RSI (Relative Strength Index)
        delta = c.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        # Calculate Bollinger Bands
        rolling_mean = c.rolling(window=boll_period).mean()
        rolling_std = c.rolling(window=boll_period).std()
        boll_upper = rolling_mean + (boll_std * rolling_std)
        boll_lower = rolling_mean - (boll_std * rolling_std)
        boll_middle = rolling_mean

        # Buy signal condition: RSI crosses above 50 and price is above the middle Bollinger Band
        buy_signal = ((rsi.shift(1) <= 50) & (rsi > 50) & (c > boll_middle))

        # Sell signal condition: Price breaks below the middle Bollinger Band and reaches the lower Bollinger Band
        sell_signal = ((c.shift(1) > boll_middle) & (
            c < boll_middle) & (c <= boll_lower))

        # Signal output: 1 for buy, -1 for sell, 0 for no action
        s = buy_signal.astype(int) - sell_signal.astype(int)

        return s

    @staticmethod
    def ret_ma50_cross_sig_price(d):
        c = d.close
        volume = d.volume
        long_T = 30
        short_T = 5

        # 计算移动平均线
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()

        # 计算EMV
        emv = (c.diff() / c.shift(1) * volume).rolling(window=short_T).sum()

        # 判断移动平均线是否大于50
        ma_condition = ma_long > 50

        # 判断EMV是否穿越0轴
        emv_cross_up = (emv > 0) & (emv.shift(1) <= 0)  # EMV由下往上穿越0轴
        emv_cross_down = (emv < 0) & (emv.shift(1) >= 0)  # EMV由上往下穿越0轴

        # 生成信号
        buy_signal = ma_condition & emv_cross_up  # 中期买进信号
        sell_signal = ma_condition & emv_cross_down  # 中期卖出信号

        # 将信号转换为数值
        signal = buy_signal.astype(int) - sell_signal.astype(int)

        return signal

    @staticmethod
    def ret_ma_bbi_rsi_sig_price(d):
        c = d.close      # 收盘价
        v = d.volume     # 成交量
        long_T = 30      # 长期均线窗口
        short_T = 5      # 短期均线窗口
        kdj_period = 14  # KDJ指标的计算周期
        bbi_short_T = 5  # BBI的短期均线周期
        bbi_long_T = 10  # BBI的长期均线周期

        # 计算短期和长期的移动平均
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()

        # 计算KDJ指标
        low_min = c.rolling(window=kdj_period).min()
        high_max = c.rolling(window=kdj_period).max()
        rsv = 100 * (c - low_min) / (high_max - low_min)  # RSV值
        k = rsv.ewm(com=2, adjust=False).mean()  # K值
        d = k.ewm(com=2, adjust=False).mean()  # D值
        j = 3 * k - 2 * d  # J值

        # KDJ金叉条件：K线突破D线且KDJ值小于25
        kdj_cross_up = (k > d) & (k.shift(1) <= d.shift(1))  # KDJ金叉
        kdj_below_25 = j < 25  # KDJ值小于25
        buy_signal_kdj = kdj_cross_up & kdj_below_25

        # KDJ死叉条件：D线突破K线且KDJ值大于75
        kdj_cross_down = (k < d) & (k.shift(1) >= d.shift(1))  # KDJ死叉
        kdj_above_75 = j > 75  # KDJ值大于75
        sell_signal_kdj = kdj_cross_down & kdj_above_75

        # 计算BBI（买卖平衡指数）
        ma_5 = c.rolling(window=5).mean()
        ma_10 = c.rolling(window=10).mean()
        ma_20 = c.rolling(window=20).mean()
        bbi = (ma_5 + ma_10 + ma_20) / 3  # BBI = (5日均线 + 10日均线 + 20日均线) / 3

        # BBI突破双底颈线：BBI突破前期低点
        bbi_double_bottom = bbi > bbi.shift(1)  # BBI突破前一日

        # 买入信号：MA在50之上且KDJ金叉且KDJ小于25，同时BBI突破双底颈线
        buy_signal_bbi = (ma_short > 50) & buy_signal_kdj & bbi_double_bottom

        # 卖出信号：KDJ死叉且KDJ大于75
        sell_signal_bbi = sell_signal_kdj

        # 合并信号：1表示买入，-1表示卖出，0表示没有信号
        signals = buy_signal_bbi.astype(int) - sell_signal_bbi.astype(int)

        return signals

    @staticmethod
    def ret_ma_short_long_cross_sig_price(d):
        c = d.close
        long_T1 = 30  # 长期平均线1
        long_T2 = 60  # 长期平均线2
        short_T = 5   # 短期平均线

        # 计算短期和长期移动平均线
        ma_short = c.rolling(window=short_T).mean()
        ma_long1 = c.rolling(window=long_T1).mean()
        ma_long2 = c.rolling(window=long_T2).mean()

        # 确保长期移动平均线大于50
        ma_condition = (ma_long1 > 50) & (ma_long2 > 50)

        # 判断短期平均线是否上穿长期平均线
        crossover_signal1 = (ma_short > ma_long1) & (
            ma_short.shift(1) <= ma_long1.shift(1))
        crossover_signal2 = (ma_short > ma_long2) & (
            ma_short.shift(1) <= ma_long2.shift(1))

        # 结合条件，产生最终信号
        signal = ma_condition.astype(
            int) * (crossover_signal1 | crossover_signal2).astype(int)

        return signal

    @staticmethod
    def ret_mfi_sig_price(d):
        c = d.close
        high = d.high
        low = d.low
        volume = d.volume

        # MFI 计算
        typical_price = (high + low + c) / 3
        money_flow = typical_price * volume
        positive_flow = (typical_price > typical_price.shift(1)) * money_flow
        negative_flow = (typical_price < typical_price.shift(1)) * money_flow

        # 计算 MFI
        mfi = 100 * positive_flow.rolling(window=14).sum() / (
            positive_flow.rolling(window=14).sum() + negative_flow.rolling(window=14).sum())

        # 买入信号: MFI 上穿 80
        buy_signal = (mfi.shift(1) < 80) & (mfi >= 80)

        # 卖出信号: MFI 下穿 20
        sell_signal = (mfi.shift(1) > 20) & (mfi <= 20)

        # 输出信号：1 表示买入，-1 表示卖出，0 表示无操作
        signal = pd.Series(0, index=d.index)
        signal[buy_signal] = 1  # 买入信号
        signal[sell_signal] = -1  # 卖出信号

        return signal

    @staticmethod
    def ret_stc_sig_price(d):
        c = d.close
        long_T = 30
        short_T = 5

        # 计算短期和长期的均线
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()

        # 计算短期和长期的最高价与最低价
        high_short = c.rolling(window=short_T).max()
        high_long = c.rolling(window=long_T).max()
        low_short = c.rolling(window=short_T).min()
        low_long = c.rolling(window=long_T).min()

        # 计算成交量的短期和长期和
        vol_short = c.rolling(window=short_T).sum()
        vol_long = c.rolling(window=long_T).sum()

        # 计算上穿下穿的条件
        condu = ((((ma_short > ma_long).astype(int).diff() == 1).astype(
            int) + (high_short >= high_long).astype(int)) == 2).astype(int)
        condd = ((((ma_short < ma_long).astype(int).diff() == 1).astype(
            int) + (low_short <= low_long).astype(int)) == 2).astype(int)

        # 计算成交量的相对值
        ft = vol_short / vol_long

        # 计算买入卖出信号的基础信号
        s = condu - condd

        # --- STC Indicator Calculation ---

        # 计算快速和慢速移动平均线
        fast_k = c.rolling(window=short_T).mean()  # 快速移动平均
        slow_d = c.rolling(window=long_T).mean()   # 慢速移动平均

        # 计算 STC 值
        stc = 100 * (fast_k - slow_d) / slow_d  # 简化的 STC 公式，实际可根据需要调整

        # 根据 STC 判断买入卖出信号
        buy_signal = (stc > 25).astype(int)  # 上穿 25
        sell_signal = (stc < 75).astype(int)  # 下穿 75

        # 最终的信号：买卖信号
        final_signal = buy_signal - sell_signal

        return final_signal

    @fill_window_800()
    @staticmethod
    def d_lib001(d):
        c = d.close

        # 计算AO指标
        fast_ma = c.rolling(window=5).mean()  # 快速移动平均线（5周期）
        slow_ma = c.rolling(window=34).mean()  # 慢速移动平均线（34周期）
        ao = fast_ma - slow_ma  # AO = 快速MA - 慢速MA

        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        # AO由负变正产生买入信号
        for i in range(1, len(ao)):
            if ao[i - 1] < 0 and ao[i] > 0:  # AO由负变正
                buy_signal[i] = 1  # 产生买入信号

            # AO由正变负产生卖出信号
            elif ao[i - 1] > 0 and ao[i] < 0:  # AO由正变负
                sell_signal[i] = -1  # 产生卖出信号

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal + sell_signal

        return signals

    @fill_window_800()
    @staticmethod
    def d_lib002(d):
        c = d.close
        low = d.low
        high = d.high
        period = 14  # 布林带和 RSI 的标准计算周期
        rsi_period = 14  # RSI 的计算周期

        # 计算 RSI
        delta = c.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        # 计算布林带
        rolling_mean = c.rolling(window=period).mean()
        rolling_std = c.rolling(window=period).std()
        lower_band = rolling_mean - (rolling_std * 2)  # 下轨
        upper_band = rolling_mean + (rolling_std * 2)  # 上轨

        # 买入信号：价格触及布林带下轨且 RSI 低于 30（超卖）
        buy_signal = ((c <= lower_band) & (rsi < 30)).astype(int)

        # 卖出信号：价格触及布林带上轨且 RSI 高于 70（超买）
        sell_signal = ((c >= upper_band) & (rsi > 70)).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal - sell_signal

        return signals

    @fill_window_800()
    @staticmethod
    def d_lib003(d):
        c = d.close
        high = d.high
        low = d.low

        # 计算CCI（Commodity Channel Index）
        period = 14
        typical_price = (high + low + c) / 3  # 典型价格
        sma = typical_price.rolling(window=period).mean()  # 典型价格的简单移动平均
        mean_deviation = (
            typical_price - sma).abs().rolling(window=period).mean()  # 平均偏差
        cci = (typical_price - sma) / (0.015 * mean_deviation)  # CCI公式

        # 计算斐波那契回撤水平
        fib_levels = [0.236, 0.382, 0.5, 0.618]  # 常见的斐波那契回撤水平
        fib_retracements = pd.DataFrame(index=c.index)

        # 计算每个周期的高低价差
        high_max = high.rolling(window=period).max()
        low_min = low.rolling(window=period).min()

        # 计算斐波那契回撤位
        fib_retracements['fib_23_6'] = high_max - \
            (high_max - low_min) * fib_levels[0]
        fib_retracements['fib_38_2'] = high_max - \
            (high_max - low_min) * fib_levels[1]
        fib_retracements['fib_50_0'] = high_max - \
            (high_max - low_min) * fib_levels[2]
        fib_retracements['fib_61_8'] = high_max - \
            (high_max - low_min) * fib_levels[3]

        # 买卖信号
        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        for i in range(1, len(c)):
            # 如果CCI大于100且当前价格接近斐波那契回撤23.6%或者38.2%
            if cci[i] > 100 and (abs(c[i] - fib_retracements['fib_23_6'][i]) < 0.02 * (high_max[i] - low_min[i]) or
                                 abs(c[i] - fib_retracements['fib_38_2'][i]) < 0.02 * (high_max[i] - low_min[i])):
                sell_signal[i] = -1  # 卖出信号

            # 如果CCI小于-100且当前价格接近斐波那契回撤61.8%或者50%
            elif cci[i] < -100 and (abs(c[i] - fib_retracements['fib_61_8'][i]) < 0.02 * (high_max[i] - low_min[i]) or
                                    abs(c[i] - fib_retracements['fib_50_0'][i]) < 0.02 * (high_max[i] - low_min[i])):
                buy_signal[i] = 1  # 买入信号

        # 综合信号：买入信号为 +1，卖出信号为 -1
        s = buy_signal + sell_signal

        return s

    @fill_window_800()
    @staticmethod
    def d_lib004(d):
        c = d.close
        high = d.high
        low = d.low

        # 唐奇安通道参数
        dc_period = 20  # 唐奇安通道的周期
        upper = high.rolling(window=dc_period).max()
        lower = low.rolling(window=dc_period).min()
        middle = (upper + lower) / 2

        # BBI指标参数
        bbi_short = 5
        bbi_medium = 10
        bbi_long = 20
        bbi_very_long = 60

        sma_5 = c.rolling(window=bbi_short).mean()
        sma_10 = c.rolling(window=bbi_medium).mean()
        sma_20 = c.rolling(window=bbi_long).mean()
        sma_60 = c.rolling(window=bbi_very_long).mean()

        bbi = (sma_5 + sma_10 + sma_20 + sma_60) / 4

        # 生成信号
        buy_signal = ((c > middle) & (c > bbi))  # 收盘价在唐奇安通道上轨以上且高于BBI
        sell_signal = ((c < middle) & (c < bbi))  # 收盘价在唐奇安通道下轨以下且低于BBI

        # 返回买卖信号，买为1，卖为-1，其他为0
        s = buy_signal.astype(int) - sell_signal.astype(int)

        return s

    @fill_window_800()
    @staticmethod
    def d_lib005(d):
        c = d.close
        long_T = 30  # 长期移动平均的周期
        short_T = 5  # 短期DPO的周期
        ma_T = 10  # 用来判断趋势的移动平均周期

        # 计算 DPO 值：当前价格 - 长期移动平均
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        dpo = c - ma_short  # DPO是当前价格与短期移动平均的差值

        # 计算MA值来判断趋势方向
        ma = c.rolling(window=ma_T).mean()  # 用ma_T来计算一个中期的简单移动平均

        # 买卖信号：
        # 1. DPO值为正并且价格高于MA时，认为是买入信号
        # 2. DPO值为负并且价格低于MA时，认为是卖出信号
        buy_signal = (dpo > 0) & (c > ma)
        sell_signal = (dpo < 0) & (c < ma)

        # 输出信号，买入信号为1，卖出信号为-1，其他为0
        s = buy_signal.astype(int) - sell_signal.astype(int)

        return s

    @fill_window_800()
    @staticmethod
    def d_lib006(d):
        c = d.close
        # 计算指数加权移动平均（EMA）
        ema_period = 14
        ema = c.ewm(span=ema_period, adjust=False).mean()

        # 归一化ENA指标
        max_ema = ema.max()  # 计算EMA的最大值
        min_ema = ema.min()  # 计算EMA的最小值
        ena = (ema - min_ema) / (max_ema - min_ema)  # 归一化ENA

        # 计算ENA的信号线（EMA的移动平均）
        ena_signal = ena.ewm(span=9, adjust=False).mean()  # 9周期的EMA作为信号线

        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        # ENA由下向上穿越信号线时产生买入信号
        for i in range(1, len(ena)):
            if ena[i - 1] < ena_signal[i - 1] and ena[i] > ena_signal[i]:  # ENA由下向上穿越信号线
                buy_signal[i] = 1  # 产生买入信号

            # ENA由上向下穿越信号线时产生卖出信号
            elif ena[i - 1] > ena_signal[i - 1] and ena[i] < ena_signal[i]:  # ENA由上向下穿越信号线
                sell_signal[i] = -1  # 产生卖出信号

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal + sell_signal

        return signals

    @fill_window_800()
    @staticmethod
    def d_lib007(d):
        c = d.close
        high = d.high
        low = d.low

        # 设置参数
        kc_period = 20  # 均线计算周期
        atr_period = 14  # ATR计算周期
        atr_multiplier = 2  # ATR倍数

        # 计算20日均线
        ma = c.rolling(window=kc_period).mean()

        # 计算ATR
        tr1 = high - low
        tr2 = (high - c.shift(1)).abs()
        tr3 = (low - c.shift(1)).abs()
        true_range = tr1.combine(tr2, max).combine(tr3, max)
        atr = true_range.rolling(window=atr_period).mean()

        # 计算Keltner Channel上下轨
        kc_upper = ma + (atr * atr_multiplier)  # KC上轨
        kc_lower = ma - (atr * atr_multiplier)  # KC下轨

        # 生成买入和卖出信号
        buy_signal = (c < kc_lower).astype(int)  # 当价格跌破KC下轨时买入
        sell_signal = (c > kc_upper).astype(int)  # 当价格突破KC上轨时卖出

        # 合并信号：1为买入，-1为卖出，0为中性
        signals = buy_signal - sell_signal

        return signals

    @fill_window_800()
    @staticmethod
    def d_lib008(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算机布林带
        def compute_bollinger_bands(c, window=20, num_std_dev=2):

            middle_band = c.rolling(window).mean()
            upper_band = middle_band + (c.rolling(window).std() * num_std_dev)
            lower_band = middle_band - (c.rolling(window).std() * num_std_dev)

            return middle_band, upper_band, lower_band

        middle_band, upper_band, lower_band = compute_bollinger_bands(c)

        # 创建买入信号
        # condu1 = ((c > ma120) & (c.shift(1) <= middle_band.shift(1)) & (c > middle_band)).astype(int)
        # 创建加仓买入信号
        condu = ((c > ma120) & (c < middle_band) & (c.shift(1) >=
                 lower_band.shift(1)) & (c > lower_band)).astype(int)

        # 创建卖出信号
        # condd1 = ((c < ma120) & (c.shift(1) >= middle_band.shift(1)) & (c < middle_band)).astype(int)
        # 创建加仓买入信号
        condd = ((c < ma120) & (c > middle_band) & (c.shift(1) <=
                 upper_band.shift(1)) & (c < upper_band)).astype(int)

        s = condu - condd

        return s

    @fill_window_800()
    @staticmethod
    def d_lib009(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算 CCI
        def compute_rsi(d, window=14):

            typical_price = (d.high + d.low + d.close) / 3
            sma = typical_price.rolling(window=window).mean()
            mad = (typical_price - sma).abs().rolling(window=window).mean()
            cci = (typical_price - sma) / (0.015 * mad)

            return cci

        cci_20 = compute_rsi(d, 20)

        # 创建买入信号
        condu = ((c > ma120) & (cci_20 < -100) & (c > c.shift(1))).astype(int)

        # 创建卖出信号
        condd = ((c < ma120) & (cci_20 > 100) & (c < c.shift(1))).astype(int)

        s = condu - condd

        return s

    @fill_window_800()
    @staticmethod
    def d_lib010(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算 MACD
        def compute_macd(c):

            ema12 = c.ewm(span=12, adjust=False).mean()
            ema26 = c.ewm(span=26, adjust=False).mean()
            macd = ema12 - ema26
            signal = macd.ewm(span=9, adjust=False).mean()

            return macd, signal

        macd, signal = compute_macd(c)

        # 创建买入信号
        condu = ((c > ma120) & (macd > 0) & (macd > signal) &
                 (macd.shift(1) <= signal.shift(1))).astype(int)

        # 创建卖出信号
        condd = ((c < ma120) & (macd < 0) & (macd < signal) &
                 (macd.shift(1) >= signal.shift(1))).astype(int)

        s = condu - condd

        return s

    @fill_window_800()
    @staticmethod
    def d_lib011(d):

        c = d.close

        # 计算 EMA
        def ema(series, span):
            return series.ewm(span=span, adjust=False).mean()

        # 计算 MACD
        ema12 = ema(c, 12)
        ema26 = ema(c, 26)
        macd = ema12 - ema26
        signal = ema(macd, 9)

        # 创建买入信号
        condu = ((macd > 0) & (macd > signal)).astype(int)

        # 创建卖出信号
        condd = ((macd < 0) & (macd < signal)).astype(int)

        s = condu - condd

        return s

    @fill_window_800()
    @staticmethod
    def d_lib012(d):
        c = d.close  # Get closing prices
        short_T = 12  # Short-term EMA period
        long_T = 26   # Long-term EMA period
        signal_T = 9  # Signal line period (MACD signal line)

        # Calculate short-term and long-term EMAs
        ema_short = c.ewm(span=short_T, adjust=False).mean()
        ema_long = c.ewm(span=long_T, adjust=False).mean()

        # Calculate DIF (Difference between short-term and long-term EMAs)
        dif = ema_short - ema_long

        # Calculate MACD (Signal line is an EMA of the DIF)
        macd = dif.ewm(span=signal_T, adjust=False).mean()

        # Calculate the difference between closing price and MACD for bar height
        bar = c - macd

        # Detect Golden Cross (DIF crosses above MACD)
        golden_cross = ((dif > macd) & (
            dif.shift(1) <= macd.shift(1))).astype(int)

        # Detect Death Cross (DIF crosses below MACD)
        death_cross = ((dif < macd) & (
            dif.shift(1) >= macd.shift(1))).astype(int)

        # Detect shrinking bars (current bar is smaller than the previous one)
        bar_shrink = (bar.abs() < bar.shift(1).abs()).astype(int)

        # Buy signal: Golden cross with shrinking green bars
        buy_signal = golden_cross & bar_shrink

        # Sell signal: Death cross with shrinking red bars
        sell_signal = death_cross & bar_shrink

        # Return buy and sell signals
        return buy_signal - sell_signal  # 1 for buy, -1 for sell, 0 for no action

    @fill_window_800()
    @staticmethod
    def d_lib013(d):
        c = d.close
        period_short = 12  # 快速EMA的周期
        period_long = 26   # 慢速EMA的周期
        signal_period = 9  # 信号线的周期

        # 计算快速和慢速EMA
        ema_short = c.ewm(span=period_short, adjust=False).mean()
        ema_long = c.ewm(span=period_long, adjust=False).mean()

        # 计算 MACD 线
        macd_line = ema_short - ema_long

        # 计算信号线
        signal_line = macd_line.ewm(span=signal_period, adjust=False).mean()

        # 买入信号：MACD 线向上穿越信号线
        golden_cross_buy = ((macd_line.shift(1) < signal_line.shift(1)) & (
            macd_line >= signal_line)).astype(int)

        # 卖出信号：MACD 线向下穿越信号线
        death_cross_sell = ((macd_line.shift(1) > signal_line.shift(1)) & (
            macd_line <= signal_line)).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = golden_cross_buy - death_cross_sell

        return signals

    @fill_window_800()
    @staticmethod
    def d_lib014(d):
        c = d.close      # 收盘价
        v = d.volume     # 成交量
        long_T = 30      # 长期均线窗口
        short_T = 5      # 短期均线窗口
        kdj_period = 14  # KDJ指标的计算周期
        bbi_short_T = 5  # BBI的短期均线周期
        bbi_long_T = 10  # BBI的长期均线周期

        # 计算短期和长期的移动平均
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()

        # 计算KDJ指标
        low_min = c.rolling(window=kdj_period).min()
        high_max = c.rolling(window=kdj_period).max()
        rsv = 100 * (c - low_min) / (high_max - low_min)  # RSV值
        k = rsv.ewm(com=2, adjust=False).mean()  # K值
        d = k.ewm(com=2, adjust=False).mean()  # D值
        j = 3 * k - 2 * d  # J值

        # KDJ金叉条件：K线突破D线且KDJ值小于25
        kdj_cross_up = (k > d) & (k.shift(1) <= d.shift(1))  # KDJ金叉
        kdj_below_25 = j < 25  # KDJ值小于25
        buy_signal_kdj = kdj_cross_up & kdj_below_25

        # KDJ死叉条件：D线突破K线且KDJ值大于75
        kdj_cross_down = (k < d) & (k.shift(1) >= d.shift(1))  # KDJ死叉
        kdj_above_75 = j > 75  # KDJ值大于75
        sell_signal_kdj = kdj_cross_down & kdj_above_75

        # 计算BBI（买卖平衡指数）
        ma_5 = c.rolling(window=5).mean()
        ma_10 = c.rolling(window=10).mean()
        ma_20 = c.rolling(window=20).mean()
        bbi = (ma_5 + ma_10 + ma_20) / 3  # BBI = (5日均线 + 10日均线 + 20日均线) / 3

        # BBI突破双底颈线：BBI突破前期低点
        bbi_double_bottom = bbi > bbi.shift(1)  # BBI突破前一日

        # 买入信号：MA在50之上且KDJ金叉且KDJ小于25，同时BBI突破双底颈线
        buy_signal_bbi = (ma_short > 50) & buy_signal_kdj & bbi_double_bottom

        # 卖出信号：KDJ死叉且KDJ大于75
        sell_signal_bbi = sell_signal_kdj

        # 合并信号：1表示买入，-1表示卖出，0表示没有信号
        signals = buy_signal_bbi.astype(int) - sell_signal_bbi.astype(int)

        return signals

    @fill_window_800()
    @staticmethod
    def d_lib015(d):
        """
        Generates trading signals based on the MA level and CCI threshold:
        - Long entry if MA is above 50 and CCI > 100.
        - Short entry if MA is below 50 and CCI < -100.
        """
        close = d.close
        high = d.high
        low = d.low

        # Parameters
        ma_period = 50  # Moving Average period for trend detection
        cci_period = 20  # CCI period (commonly used)
        cci_threshold = 100  # Threshold for CCI

        # Calculate MA
        ma = close.rolling(window=ma_period).mean()

        # Calculate CCI
        tp = (high + low + close) / 3  # Typical Price for CCI calculation
        cci = (tp - tp.rolling(window=cci_period).mean()) / \
            (0.015 * tp.rolling(window=cci_period).std())

        # Generate Signals
        long_entry = ((ma > 50) & (cci > cci_threshold)).astype(int)
        short_entry = ((ma < 50) & (cci < -cci_threshold)).astype(int)

        # Signal output: 1 for long, -1 for short
        signals = long_entry - short_entry

        return signals

    @fill_window_800()
    @staticmethod
    def d_lib016(d):
        c = d.close
        long_T1 = 30  # 长期平均线1
        long_T2 = 60  # 长期平均线2
        short_T = 5   # 短期平均线

        # 计算短期和长期移动平均线
        ma_short = c.rolling(window=short_T).mean()
        ma_long1 = c.rolling(window=long_T1).mean()
        ma_long2 = c.rolling(window=long_T2).mean()

        # 确保长期移动平均线大于50
        ma_condition = (ma_long1 > 50) & (ma_long2 > 50)

        # 判断短期平均线是否上穿长期平均线
        crossover_signal1 = (ma_short > ma_long1) & (
            ma_short.shift(1) <= ma_long1.shift(1))
        crossover_signal2 = (ma_short > ma_long2) & (
            ma_short.shift(1) <= ma_long2.shift(1))

        # 结合条件，产生最终信号
        signal = ma_condition.astype(
            int) * (crossover_signal1 | crossover_signal2).astype(int)

        return signal

    @fill_window_800()
    @staticmethod
    def d_lib017(d, momentum_period=14):
        c = d.close  # 获取收盘价
        # 计算动量值
        momentum = c.diff(periods=momentum_period)  # 动量值 = 当前收盘价 - 过去指定周期的收盘价

        # 买入信号：动量值大于0并上升
        golden_cross_buy = ((momentum > 0) & (
            momentum > momentum.shift(1))).astype(int)

        # 卖出信号：动量值小于0并下降
        death_cross_sell = ((momentum < 0) & (
            momentum < momentum.shift(1))).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = golden_cross_buy - death_cross_sell

        return signals  # 返回信号序列

    @fill_window_800()
    @staticmethod
    def d_lib018(d, short_period=9, long_period=26):
        c = d.close

        # 计算短期和长期 EMA
        ema_short = c.ewm(span=short_period, adjust=False).mean()
        ema_long = c.ewm(span=long_period, adjust=False).mean()

        # 计算 PO
        po = (ema_short - ema_long) / ema_long * 100

        # 买卖信号
        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        for i in range(1, len(po)):
            # PO 上穿 0 时，产生买入信号
            if po[i - 1] <= 0 and po[i] > 0:
                buy_signal[i] = 1  # 买入信号

            # PO 下穿 0 时，产生卖出信号
            elif po[i - 1] >= 0 and po[i] < 0:
                sell_signal[i] = -1  # 卖出信号

        # 综合信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal + sell_signal

        return signals

    @fill_window_800()
    @staticmethod
    def d_lib019(d):
        c = d.close
        high = d.high
        low = d.low

        # Parameters for Bollinger Bands and RSI
        rsi_period = 14
        boll_period = 20
        boll_std = 2

        # Calculate the RSI (Relative Strength Index)
        delta = c.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        # Calculate Bollinger Bands
        rolling_mean = c.rolling(window=boll_period).mean()
        rolling_std = c.rolling(window=boll_period).std()
        boll_upper = rolling_mean + (boll_std * rolling_std)
        boll_lower = rolling_mean - (boll_std * rolling_std)
        boll_middle = rolling_mean

        # Buy signal condition: RSI crosses above 50 and price is above the middle Bollinger Band
        buy_signal = ((rsi.shift(1) <= 50) & (rsi > 50) & (c > boll_middle))

        # Sell signal condition: Price breaks below the middle Bollinger Band and reaches the lower Bollinger Band
        sell_signal = ((c.shift(1) > boll_middle) & (
            c < boll_middle) & (c <= boll_lower))

        # Signal output: 1 for buy, -1 for sell, 0 for no action
        s = buy_signal.astype(int) - sell_signal.astype(int)

        return s

    @fill_window_800()
    @staticmethod
    def d_lib020(d):
        c = d.close
        td_buy_count = 0
        td_sell_count = 0
        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        # TD 买入结构
        for i in range(4, len(c)):
            # 买入结构
            if c[i] < c[i - 4]:  # 当前收盘价低于4天前收盘价
                td_buy_count += 1
            else:
                td_buy_count = 0  # 重置计数

            # 满足连续9天买入条件
            if td_buy_count == 9:
                buy_signal[i] = 1  # 产生买入信号
                td_buy_count = 0  # 重置计数器

            # 卖出结构
            if c[i] > c[i - 4]:  # 当前收盘价高于4天前收盘价
                td_sell_count += 1
            else:
                td_sell_count = 0  # 重置计数

            # 满足连续9天卖出条件
            if td_sell_count == 9:
                sell_signal[i] = -1  # 产生卖出信号
                td_sell_count = 0  # 重置计数器

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal + sell_signal

        return signals

    @fill_window_800()
    @staticmethod
    def d_lib021(d, period=14):
        c = d.close  # 获取收盘价

        # 计算威廉指标（Williams %R）
        highest_high = d.high.rolling(window=period).max()  # 过去14天的最高价
        lowest_low = d.low.rolling(window=period).min()     # 过去14天的最低价
        williams_r = -100 * (highest_high - c) / \
            (highest_high - lowest_low)  # Williams %R 计算

        # 买入信号：威廉指标低于-80
        buy_signal = williams_r < -80

        # 卖出信号：威廉指标高于-20
        sell_signal = williams_r > -20

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal.astype(int) - sell_signal.astype(int)

        return signals  # 返回信号序列
