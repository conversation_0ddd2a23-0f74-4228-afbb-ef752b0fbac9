#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的资金计算bug
"""

import sys
sys.path.append('.')

import pandas as pd
from vnpy_backtester.scripts.run_multi_position_strategy import run_multi_position_strategy_backtest

def test_capital_calculation_fix():
    """测试资金计算修复"""
    print('=== 测试修复后的资金计算 ===')
    
    # 读取数据
    try:
        df = pd.read_pickle(r'C:\Users\<USER>\Desktop\kline_data_2025-05-30_14儶57儶15.pkl')
        df['signals'] = df['signal']
        df.index = pd.to_datetime(df.index)
        print('数据加载成功')
    except Exception as e:
        print(f'数据加载失败: {e}')
        return
    
    # 测试参数
    test_params = {
        'holding_bars': 10,
        'position_size': 1000,
        'rate': 0.0003,
        'slippage': 0.000,
        'capital': 15000,
        'order_type': 'amount',
        'max_positions': 10,
        'plot_show': False,
        'plot_save': False
    }
    
    print('\n测试1: 07-01到07-15期间')
    try:
        df1 = df['2023-07-01':'2023-07-15']
        print(f'数据长度: {len(df1)}')
        
        engine1 = run_multi_position_strategy_backtest(df=df1, **test_params)
        stats1 = engine1.calculate_statistics()
        end_balance1 = stats1['end_balance']
        print(f'最终资金: {end_balance1:,.2f}')
        
    except Exception as e:
        print(f'测试1失败: {e}')
        import traceback
        traceback.print_exc()
        return
    
    print('\n测试2: 07-11到07-15期间')
    try:
        df2 = df['2023-07-11':'2023-07-15']
        print(f'数据长度: {len(df2)}')
        
        engine2 = run_multi_position_strategy_backtest(df=df2, **test_params)
        stats2 = engine2.calculate_statistics()
        end_balance2 = stats2['end_balance']
        print(f'最终资金: {end_balance2:,.2f}')
        
    except Exception as e:
        print(f'测试2失败: {e}')
        import traceback
        traceback.print_exc()
        return
    
    # 对比结果
    print('\n=== 对比结果 ===')
    difference = abs(end_balance1 - end_balance2)
    print(f'测试1最终资金: {end_balance1:,.2f}')
    print(f'测试2最终资金: {end_balance2:,.2f}')
    print(f'资金差异: {difference:,.2f}')
    
    if difference < 1000:
        print('✅ 修复成功！资金差异已大幅减少')
    else:
        print('❌ 仍有较大差异，需要进一步检查')
        print(f'差异百分比: {difference / min(end_balance1, end_balance2) * 100:.2f}%')

if __name__ == '__main__':
    test_capital_calculation_fix()
