import pandas as pd


def fill_window_realtime(signals, window=5):
    s_modified = signals.copy()  # 复制原信号
    for i in range(1, len(signals)):  # 从第二个点开始遍历
        if s_modified.iloc[i] == 0:  # 只有在当前无信号时才填充
            recent_signals = s_modified.iloc[max(
                0, i-window):i]  # 取过去 window 内的信号
            # 找到最近的非零信号
            last_signal = recent_signals[recent_signals !=
                                         0].iloc[-1:]
            if not last_signal.empty:
                # 继承最近的信号
                s_modified.iloc[i] = last_signal.values[0]

    return s_modified


def fill_window(signals, window=5):
    s_modified = pd.Series(0, index=signals.index)  # 初始化全0

    idx_1 = signals[signals == 1].index
    idx_neg1 = signals[signals == -1].index

    s_pos = pd.Series(0, index=signals.index)
    s_neg = pd.Series(0, index=signals.index)

    for i in idx_1:
        s_pos.iloc[i:i + window] = 1  # 让后续100个点变成 1
    for i in idx_neg1:
        s_neg.iloc[i:i + window] = -1  # 让后续100个点变成 -1

    s_modified = s_pos + s_neg
    return s_modified


sig = pd.Series([1, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1])
# 1 1 0 0 0 -1 -1 0 0 0 0 0 0 0 0
print(fill_window(sig))
