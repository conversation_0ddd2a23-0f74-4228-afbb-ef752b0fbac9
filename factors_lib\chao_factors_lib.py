import pandas as pd
import numpy as np


class ChaoFactorLib:
    def __init__(self):
        pass

    @staticmethod
    def c_chu001(df):
        '''衡量当前波动率高低的过滤器'''
        log_ratio = np.log(df['close'] / df['close'].shift(1))
        hv = log_ratio.rolling(20).std()
        return hv

    @staticmethod
    def c_chu002(df):
        '''衡量当前成交量高低的过滤器'''
        volume_mean = df['volume'].rolling(20).mean()
        volume_deviation = (df['volume'] - volume_mean) / volume_mean
        return volume_deviation

    @staticmethod
    def c_chu003(df):
        '''衡量当前相对位置高低的过滤器'''
        up = df['high'].rolling(20).max()
        down = df['low'].rolling(20).min()
        price_position = (df['close'] - down) / (up - down)
        return price_position

    @staticmethod
    def c_chu004(df):
        '''衡量短期价格波动快慢的过滤器'''
        std_5 = df['close'].rolling(5).std()
        std_30 = df['close'].rolling(30).std()
        price_fluctuation = std_5 / std_30
        return price_fluctuation

    @staticmethod
    def c_chu005(df, window=14):
        '''计算atr因子'''
        tr1 = df['high'] - df['low']
        tr2 = abs(df['high'] - df['close'].shift(1))
        tr3 = abs(df['close'].shift(1) - df['low'])
        tr = pd.DataFrame({'tr1': tr1, 'tr2': tr2, 'tr3': tr3}).max(axis=1)
        atr = tr.ewm(alpha=1 / window, adjust=False).mean()
        return atr

    @staticmethod
    def c_chu006(df, window=14):
        '''计算rsi因子'''
        close_diff = df['close'].diff()
        u = np.where(close_diff > 0, close_diff, 0)
        d = np.where(close_diff < 0, -close_diff, 0)
        smma_u = pd.Series(u).ewm(alpha=1 / window, adjust=False).mean()
        smma_d = pd.Series(d).ewm(alpha=1 / window, adjust=False).mean()
        rsi = np.where(smma_d == 0, 100,
                       np.where(smma_u == 0, 0,
                                100 * (smma_u / (smma_u + smma_d))))
        return pd.Series(rsi, index=df.index)

    @staticmethod
    def c_chu007(df):
        '''计算vwap因子'''
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        vwap = (typical_price * df['volume']).cumsum() / df['volume'].cumsum()
        return vwap

    @staticmethod
    def c_chu008(df):
        '''计算vwap背离因子'''
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        vwap = (typical_price * df['volume']).cumsum() / df['volume'].cumsum()
        vwap_deviation = (df['close'] - vwap) / vwap
        return vwap_deviation

    @staticmethod
    def c_chu009(df):
        '''计算chao1因子'''
        close_diff = df['close'] - df['close'].shift(1)
        volume_diff = df['volume'] - df['volume'].shift(1)
        chao1 = close_diff / volume_diff
        return chao1

    @staticmethod
    def c_chu010(df, window=20):
        '''计算chao3因子'''
        body_ratio = (df['close'] - df['open']) / (df['high'] - df['low'])
        chao3 = body_ratio.ewm(span=window, adjust=False).mean()
        return chao3

    @staticmethod
    def c_chu011(df):
        '''计算chao5因子'''
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        vwap = (typical_price * df['volume']).cumsum() / \
            df['volume'].cumsum()
        chao5 = np.sqrt(df['high'] * df['low']) - vwap
        return chao5

    @staticmethod
    def c_chu012(df, window=10):
        '''计算alpha_010因子'''
        numerator = (df['close'] - df['low']) - (df['high'] - df['close'])
        denominator = df['high'] - df['low']
        alpha_010 = (numerator / denominator *
                     df['volume']).rolling(window).sum()
        return alpha_010

    @staticmethod
    def c_chu013(df, window=6, ma_window=12):
        '''计算alpha_022因子'''
        mean_close_6 = df['close'].rolling(window).mean()
        deviation = (df['close'] - mean_close_6) / mean_close_6
        deviation_change = deviation - deviation.shift(1)
        alpha_022 = deviation_change.ewm(span=ma_window, adjust=False).mean()
        return alpha_022

    @staticmethod
    def c_chu014(df, window=6):
        '''计算alpha_043因子'''
        price_change = df['close'].diff(1)
        volume_contribution = pd.Series(np.where(price_change > 0, df['volume'],
                                                 np.where(price_change < 0, -df['volume'], 0)), index=df.index)
        alpha_043 = volume_contribution.rolling(window).sum()
        return alpha_043

    @staticmethod
    def c_chu015(df, window1=3, window2=6, window3=12, window4=24):
        '''计算alpha_046因子'''
        mean1 = df['close'].rolling(window1).mean()
        mean2 = df['close'].rolling(window2).mean()
        mean3 = df['close'].rolling(window3).mean()
        mean4 = df['close'].rolling(window4).mean()
        alpha_046 = (mean1 + mean2 + mean3 + mean4) / (4 * df['close'])
        return alpha_046

    @staticmethod
    def c_chu016(df, window=12):
        '''计算 alpha_050 因子'''
        high_delay1 = df['high'].shift(1)
        low_delay1 = df['low'].shift(1)

        cond1 = (df['high'] + df['low']) <= (high_delay1 + low_delay1)
        cond2 = (df['high'] + df['low']) >= (high_delay1 + low_delay1)

        delta_high = abs(df['high'] - high_delay1)
        delta_low = abs(df['low'] - low_delay1)

        # 保证数据类型统一
        max_delta = pd.Series(np.maximum(
            delta_high, delta_low), index=df.index)

        S1 = pd.Series(np.where(cond1, 0, max_delta), index=df.index)
        S2 = pd.Series(np.where(cond2, 0, max_delta), index=df.index)

        sum_S1 = S1.rolling(window).sum()
        sum_S2 = S2.rolling(window).sum()

        # 避免除零问题
        eps = 1e-9
        denom = sum_S1 + sum_S2 + eps
        alpha_050 = (sum_S1 / denom) - (sum_S2 / denom)
        return alpha_050

    @staticmethod
    def c_chu017(df, window1=9, window2=3):
        '''计算alpha_057因子'''
        rsv = (df['close'] - df['low'].rolling(window1).min()) / \
            (df['high'].rolling(window1).max() -
             df['low'].rolling(window1).min()) * 100
        alpha_057 = rsv.rolling(window2, min_periods=1).mean()
        return alpha_057

    @staticmethod
    def c_chu018(df, window=20):
        '''计算alpha_076因子'''
        close_delay = df['close'].shift(1)
        abs_return_vol = abs((df['close'] / close_delay - 1)) / df['volume']
        std_20 = abs_return_vol.rolling(window).std()
        mean_20 = abs_return_vol.rolling(window).mean()
        alpha_076 = std_20 / mean_20
        return alpha_076

    @staticmethod
    def c_chu019(df, window=22):
        '''计算alpha_080因子'''
        alpha_080 = df['volume'].ewm(alpha=2 / window, adjust=False).mean()
        return alpha_080

    @staticmethod
    def c_chu020(df, window1=11, window2=4):
        '''计算alpha_111因子'''
        M = df['volume'] * ((df['close'] - df['low']) -
                            (df['high'] - df['close'])) / (df['high'] - df['low'])
        SMA_11 = M.ewm(alpha=2 / window1, adjust=False).mean()
        SMA_4 = M.ewm(alpha=2 / window2, adjust=False).mean()
        alpha_111 = SMA_11 - SMA_4
        return alpha_111

    @staticmethod
    def c_chu021(df, window=12):
        '''计算alpha_127因子'''
        max_close_12 = df['close'].rolling(window).max()
        percent_change_sq = (
            (100 * (df['close'] - max_close_12) / max_close_12) ** 2)
        mean_sq = percent_change_sq.rolling(window).mean()
        alpha_127 = mean_sq.pow(0.5)
        return alpha_127

    @staticmethod
    def c_chu022(df):
        '''计算alpha_150因子'''
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        alpha_150 = typical_price * df['volume']
        return alpha_150

    @staticmethod
    def c_chu023(df, span=13):
        '''计算alpha_173因子'''
        alpha = 2 / (span + 1)
        sma1 = df['close'].ewm(alpha=alpha, adjust=False).mean()
        sma2 = sma1.ewm(alpha=alpha, adjust=False).mean()
        sma3 = np.log(df['close']).ewm(alpha=alpha, adjust=False).mean()
        sma3 = sma3.ewm(alpha=alpha, adjust=False).mean()
        sma3 = sma3.ewm(alpha=alpha, adjust=False).mean()
        alpha_173 = 3 * sma1 - 2 * sma2 + sma3
        return alpha_173

    @staticmethod
    def c_chu024(df, window=6):
        '''计算alpha_175因子'''
        close_delay = df['close'].shift(1)
        high_low = df['high'] - df['low']
        close_high = abs(close_delay - df['high'])
        close_low = abs(close_delay - df['low'])
        max_diff = pd.DataFrame(
            {'high_low': high_low, 'close_high': close_high, 'close_low': close_low}).max(axis=1)
        alpha_175 = max_diff.rolling(window).mean()
        return alpha_175

    @staticmethod
    def c_chu025(df, window=6):
        '''计算alpha_189因子'''
        mean_close_6 = df['close'].rolling(window).mean()
        abs_dev = (df['close'] - mean_close_6).abs()
        alpha_189 = abs_dev.rolling(window).mean()
        return alpha_189

    @staticmethod
    def c_chu026(df, window1=20, window2=10):
        '''计算alpha_191因子'''
        V_mean = df['volume'].rolling(window1).mean()
        Corr = V_mean.rolling(window2).corr(df['close'])
        Mid_Price = (df['high'] + df['low']) / 2
        alpha_191 = Corr + Mid_Price - df['close']
        return alpha_191

    @staticmethod
    def c_chu027(df):
        '''计算ats因子'''
        ats = df['volume'] / df['trade_count']
        return ats

    @staticmethod
    def c_chu028(df, window=20):
        '''计算tcv因子'''
        tcv = df['trade_count'].rolling(window).std()
        return tcv

    @staticmethod
    def c_chu029(df):
        '''计算tvs因子'''
        tvs = df['trade_count'] * df['volume']
        return tvs

    @staticmethod
    def c_chu030(df):
        '''计算k线距离因子'''
        kline_distance = (2*(df['high'] - df['low']) -
                          abs(df['close']-df['open']))/df['volume']
        return kline_distance

    @staticmethod
    def c_chu031(df, window=100):
        '''计算动量因子'''
        log_return = np.log(df['close']/df['close'].shift(1))
        rank = log_return.rolling(window).rank(pct=False)
        momentum = (rank - rank.rolling(100).min()) / (
            rank.rolling(100).max() - rank.rolling(100).min()
        )
        return momentum

    # ========3.13添加==================================================#
    @staticmethod
    def c_chu032(df):
        '''计算upper_shadow1'''
        upper_shadow = df['high']-df[['close', 'open']].max(axis=1)
        return upper_shadow

    @staticmethod
    def c_chu033(df):
        '''计算lower_shadow1'''
        计算lower_shadow = df[['close', 'open']].min(axis=1)-df['low']
        return 计算lower_shadow

    @staticmethod
    def c_chu034(df):
        '''计算upper_shadow2'''
        upper_shadow = df['high']/df[['close', 'open']].max(axis=1)
        return upper_shadow

    @staticmethod
    def c_chu035(df):
        '''计算lower_shadow2'''
        计算lower_shadow = df[['close', 'open']].min(axis=1)/df['low']
        return 计算lower_shadow

    # @staticmethod
    # ### 离散的###
    # def c_chu036(df, rsi_length=14, rsi_overbought=72, rsi_oversold=28, min_vol=500, cooldown_bars=10):
    #     # 2025/4/9添加
    #     df = df.copy()

    #     # === VWAP ===
    #     df['hlc3'] = (df['high'] + df['low'] + df['close']) / 3
    #     df['vwap'] = (df['hlc3'] * df['volume']).cumsum() / \
    #         df['volume'].cumsum()

    #     # === RSI ===
    #     delta = df['close'].diff()
    #     gain = np.where(delta > 0, delta, 0)
    #     loss = np.where(delta < 0, -delta, 0)
    #     avg_gain = pd.Series(gain, index=df.index).rolling(rsi_length).mean()
    #     avg_loss = pd.Series(loss, index=df.index).rolling(rsi_length).mean()
    #     rs = avg_gain / (avg_loss + 1e-10)
    #     df['rsi'] = 100 - (100 / (1 + rs))

    #     # === ATR ===（不是必须，仅供扩展）
    #     # tr = np.max([
    #     #     df['high'] - df['low'],
    #     #     (df['high'] - df['close'].shift()).abs(),
    #     #     (df['low'] - df['close'].shift()).abs()
    #     # ], axis=0)
    #     # df['atr'] = pd.Series(tr).rolling(atr_length).mean()

    #     # === K线 Rejection ===
    #     df['bearish_rejection'] = (df['close'] < df['close'].shift(1)) & (
    #         df['close'] > df['vwap'])
    #     df['bullish_rejection'] = (df['close'] > df['close'].shift(1)) & (
    #         df['close'] < df['vwap'])

    #     # === 初步信号 ===
    #     df['short_signal'] = (
    #         (df['rsi'] < rsi_overbought) &
    #         (df['rsi'].shift(1) >= rsi_overbought) &
    #         (df['volume'] > min_vol) &
    #         df['bearish_rejection']
    #     )

    #     df['long_signal'] = (
    #         (df['rsi'] > rsi_oversold) &
    #         (df['rsi'].shift(1) <= rsi_oversold) &
    #         (df['volume'] > min_vol) &
    #         df['bullish_rejection']
    #     )

    #     # === 冷却机制 & 最终信号 ===
    #     signals = np.zeros(len(df), dtype=int)
    #     last_long = -cooldown_bars - 1
    #     last_short = -cooldown_bars - 1

    #     for i in range(len(df)):
    #         row = df.iloc[i]
    #         if row['long_signal'] and (i - last_long > cooldown_bars):
    #             signals[i] = 1
    #             last_long = i
    #         elif row['short_signal'] and (i - last_short > cooldown_bars):
    #             signals[i] = -1
    #             last_short = i

    #     return pd.Series(signals, index=df.index, name='signal')

    # ========4.17添加==================================================#
    @staticmethod
    def c_chu036(df, window=14):
        '''计算atr因子'''
        tr1 = df['high'] - df['low']
        tr2 = abs(df['high'] - df['close'].shift(1))
        tr3 = abs(df['close'].shift(1) - df['low'])
        tr = pd.DataFrame({'tr1': tr1, 'tr2': tr2, 'tr3': tr3}).max(axis=1)
        atr = tr.ewm(alpha=1 / window, adjust=False).mean()

        return atr/df['close']

    ## ========4.22/4.23添加(新评估框架下产生的因子)==================================================#

    @staticmethod
    def c_chu037(df):
        '''计算c_chu009因子的abs'''
        close_diff = df['close'] - df['close'].shift(1)
        volume_diff = df['volume'] - df['volume'].shift(1)
        chao1 = close_diff / volume_diff
        return abs(chao1)

    @staticmethod
    def c_chu038(df, window=20):
        '''计算c_chu010因子的abs'''
        body_ratio = (df['close'] - df['open']) / (df['high'] - df['low'])
        chao3 = body_ratio.ewm(span=window, adjust=False).mean()*df['volume']
        return abs(chao3)

    @staticmethod
    def c_chu039(df, window=10):
        '''计算c_chu012因子的abs'''
        numerator = (df['close'] - df['low']) - (df['high'] - df['close'])
        denominator = df['high'] - df['low']
        alpha_010 = (numerator / denominator *
                     df['volume']).rolling(window).sum()
        return abs(alpha_010)

    @staticmethod
    def c_chu040(df, window=6, ma_window=12):
        '''计算c_chu013因子的abs'''
        mean_close_6 = df['close'].rolling(window).mean()
        deviation = (df['close'] - mean_close_6) / mean_close_6
        deviation_change = deviation - deviation.shift(1)
        alpha_022 = deviation_change.ewm(span=ma_window, adjust=False).mean()
        return abs(alpha_022)

    @staticmethod
    def c_chu041(df, window=6):
        '''计算c_chu014因子的abs'''
        price_change = df['close'].diff(1)
        volume_contribution = pd.Series(np.where(price_change > 0, df['volume'],
                                                 np.where(price_change < 0, -df['volume'], 0)), index=df.index)
        alpha_043 = volume_contribution.rolling(window).sum()
        return abs(alpha_043)

    @staticmethod
    def c_chu042(df, window1=3, window2=6, window3=12, window4=24):
        '''计算c_chu015因子-1的abs'''
        mean1 = df['close'].rolling(window1).mean()
        mean2 = df['close'].rolling(window2).mean()
        mean3 = df['close'].rolling(window3).mean()
        mean4 = df['close'].rolling(window4).mean()
        alpha_046 = (mean1 + mean2 + mean3 + mean4) / (4 * df['close'])
        return abs(alpha_046-1)

    @staticmethod
    def c_chu043(df, window1=11, window2=4):
        '''计算c_chu020因子的abs'''
        M = df['volume'] * ((df['close'] - df['low']) -
                            (df['high'] - df['close'])) / (df['high'] - df['low'])
        SMA_11 = M.ewm(alpha=2 / window1, adjust=False).mean()
        SMA_4 = M.ewm(alpha=2 / window2, adjust=False).mean()
        alpha_111 = SMA_11 - SMA_4
        return abs(alpha_111)

    @staticmethod
    def c_chu044(df, window=20):
        '''计算c_chu010因子的平方'''
        body_ratio = (df['close'] - df['open']) / (df['high'] - df['low'])
        chao3 = body_ratio.ewm(span=window, adjust=False).mean()*df['volume']
        return chao3**2

    @staticmethod
    def c_chu045(df, window=10):
        '''计算c_chu012因子的平方'''
        numerator = (df['close'] - df['low']) - (df['high'] - df['close'])
        denominator = df['high'] - df['low']
        alpha_010 = (numerator / denominator *
                     df['volume']).rolling(window).sum()
        return alpha_010**2

    @staticmethod
    def c_chu046(df, window=6, ma_window=12):
        '''计算c_chu013因子的平方'''
        mean_close_6 = df['close'].rolling(window).mean()
        deviation = (df['close'] - mean_close_6) / mean_close_6
        deviation_change = deviation - deviation.shift(1)
        alpha_022 = deviation_change.ewm(span=ma_window, adjust=False).mean()
        return alpha_022**2

    @staticmethod
    def c_chu047(df, window=6):
        '''计算c_chu014因子的平方'''
        price_change = df['close'].diff(1)
        volume_contribution = pd.Series(np.where(price_change > 0, df['volume'],
                                                 np.where(price_change < 0, -df['volume'], 0)), index=df.index)
        alpha_043 = volume_contribution.rolling(window).sum()
        return abs(alpha_043)

    @staticmethod
    def c_chu048(df, window1=3, window2=6, window3=12, window4=24):
        '''计算c_chu015因子-1的平方'''
        mean1 = df['close'].rolling(window1).mean()
        mean2 = df['close'].rolling(window2).mean()
        mean3 = df['close'].rolling(window3).mean()
        mean4 = df['close'].rolling(window4).mean()
        alpha_046 = (mean1 + mean2 + mean3 + mean4) / (4 * df['close'])
        return (alpha_046-1)**2

    @staticmethod
    def c_chu049(df, window1=11, window2=4):
        '''计算c_chu020因子的平方'''
        M = df['volume'] * ((df['close'] - df['low']) -
                            (df['high'] - df['close'])) / (df['high'] - df['low'])
        SMA_11 = M.ewm(alpha=2 / window1, adjust=False).mean()
        SMA_4 = M.ewm(alpha=2 / window2, adjust=False).mean()
        alpha_111 = SMA_11 - SMA_4
        return alpha_111**2

    # 4.24加入，技术指标
    @staticmethod
    def c_chu050(df, window1=14):
        # 计算 MAX_HIGH 和 MAX_LOW
        max_high = (df['high'] > df['high'].shift(1)).astype(
            int) * (df['high'] - df['high'].shift(1))
        max_low = (df['low'].shift(1) > df['low']).astype(
            int) * (df['low'].shift(1) - df['low'])

        # 计算 XPDM 和 XNDM
        xpdm = (max_high > max_low).astype(int) * \
            (df['high'] - df['high'].shift(1))
        xndm = (max_low > max_high).astype(int) * \
            (df['low'].shift(1) - df['low'])

        # 计算 PDM 和 NDM
        pdm = xpdm.rolling(window1).sum()
        ndm = xndm.rolling(window1).sum()

        # 计算 True Range (TR)
        tr = pd.DataFrame({
            'high_low': (df['high'] - df['low']).abs(),
            'high_close': (df['high'] - df['close'].shift(1)).abs(),
            'low_close': (df['low'] - df['close'].shift(1)).abs()
        })
        tr = tr.max(axis=1).rolling(window1).sum()

        # 计算 DI+ 和 DI-
        di_plus = pdm / tr * 100
        di_minus = ndm / tr * 100

        return di_plus+di_minus

    @staticmethod
    def c_chu051(df, N1=10, N2=40, N3=20):
        """
        计算 ROC, ROC_MA, PMO 和 PMO_SIGNAL 指标

        参数:
        df : pd.DataFrame，包含 'close' 列
        N1 : int，ROC_MA 的平滑周期（默认10）
        N2 : int，PMO 的平滑周期（默认40）
        N3 : int，PMO_SIGNAL 的平滑周期（默认20）

        返回:
        pd.DataFrame，包含 ROC, ROC_MA, PMO, PMO_SIGNAL 列
        """
        # 计算 ROC = (CLOSE - REF(CLOSE,1)) / REF(CLOSE,1) * 100
        ROC = (df['close'] - df['close'].shift(1)) / df['close'].shift(1) * 100

        # 计算 ROC_MA = DMA(ROC, 2/(N1+1)) （动态移动平均）
        alpha_roc = 2 / (N1 + 1)
        ROC_MA = ROC.ewm(alpha=alpha_roc, adjust=False).mean()

        return abs(ROC_MA)

    @staticmethod
    def c_chu052(df, N1=34, N2=55):
        """
        计算 Klinger Oscillator (克林格振荡器)

        参数:
        df : pd.DataFrame，包含 'high', 'low', 'close', 'volume' 列
        N1 : int，短期EMA周期（默认34）
        N2 : int，长期EMA周期（默认55）

        返回:
        pd.Series，包含 KO 值
        """
        # 计算典型价格
        TYPICAL = (df['high'] + df['low'] + df['close']) / 3

        # 计算方向性成交量
        VOLUME = np.where(
            TYPICAL >= TYPICAL.shift(1),
            df['volume'],
            -df['volume']
        )

        # 计算EMA
        VOLUME_EMA1 = pd.Series(VOLUME).ewm(span=N1, adjust=False).mean()
        VOLUME_EMA2 = pd.Series(VOLUME).ewm(span=N2, adjust=False).mean()

        # 计算KO线
        KO = pd.Series(VOLUME_EMA1 - VOLUME_EMA2)
        KO.index = df.index

        return abs(KO)
