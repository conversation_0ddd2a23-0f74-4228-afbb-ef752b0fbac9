import aiohttp
from itertools import chain
import asyncio
import pymysql
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')

# Binance API URL
BINANCE_API_URL = "https://fapi.binance.com/fapi/v1/klines"

# MySQL数据库配置
MYSQL_CONFIG = {
    "host": "************",
    "user": "root",
    "password": "1234",
    "database": "ctadata"
}

# 代理配置
PROXY_CONFIG = {
    "http": "http://127.0.0.1:2080"
}


def table_exists(cursor, table_name):
    cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
    return cursor.fetchone() is not None


def create_or_truncate_table(cursor, symbol, year):
    table_name = f"{symbol}_{year}"
    if table_exists(cursor, table_name):
        logging.info(f"Table {table_name} already exists. Truncating data.")
        cursor.execute(f"TRUNCATE TABLE {table_name}")
    else:
        logging.info(f"Creating table {table_name}.")
        cursor.execute(f"""
        CREATE TABLE {table_name} (
            open_time BIGINT PRIMARY KEY,
            open DECIMAL(20, 8),
            high DECIMAL(20, 8),
            low DECIMAL(20, 8),
            close DECIMAL(20, 8),
            volume DECIMAL(20, 8),
            close_time BIGINT,
            turnover DECIMAL(20, 8),
            trade_count INT,
            taker_buy_volume DECIMAL(20, 8),
            taker_buy_turnover DECIMAL(20, 8)
        )
        """)
    return table_name


async def fetch_klines(session, symbol, start_time, end_time):
    params = {
        "symbol": symbol,
        "interval": '1m',
        "startTime": start_time,
        "endTime": end_time,
        "limit": 1500
    }
    try:
        async with session.get(BINANCE_API_URL, params=params, proxy=PROXY_CONFIG["http"]) as response:
            response.raise_for_status()  # 检查请求是否成功
            return await response.json()
    except aiohttp.ClientError as e:
        logging.error(f"Failed to fetch klines: {e}")
        return []


def insert_klines(cursor, table_name, klines):
    try:
        klines = [kline[:11] for kline in klines]  # 只保留前11个字段
        cursor.executemany(f"""
            INSERT IGNORE INTO {table_name} (
            open_time, open, high, low, close, volume,
            close_time, turnover, trade_count, taker_buy_volume,
            taker_buy_turnover
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, klines)
    except pymysql.MySQLError as e:
        logging.error(f"Failed to insert kline: {e}")


async def main(symbol, start_year, end_year):

    # 连接MySQL数据库
    try:
        conn = pymysql.connect(**MYSQL_CONFIG)
        cursor = conn.cursor()
    except pymysql.MySQLError as e:
        logging.error(f"Failed to connect to MySQL: {e}")
        return

    # 创建或清空表
    try:
        table_name = create_or_truncate_table(
            cursor, symbol, f'{start_year}_{end_year}')
    except pymysql.MySQLError as e:
        logging.error(f"Failed to create or truncate table: {e}")
        cursor.close()
        conn.close()
        return

    async with aiohttp.ClientSession() as session:
        tasks = []
        completed_data = []

        day_timestamps = get_year_timestamps(start_year, end_year)
        for i in range(len(day_timestamps) - 1):
            start_time = day_timestamps[i][0]
            end_time = day_timestamps[i][1]

            tasks.append(fetch_klines(session, symbol, start_time, end_time))
            if (i + 1) % 60 == 0:
                results = await asyncio.gather(*tasks)
                completed_data = list(chain.from_iterable(results))
                if completed_data:
                    insert_klines(cursor, table_name, completed_data)
                    conn.commit()
                    logging.info(
                        f"Inserted {len(completed_data)} klines to database.")

                tasks = []
                completed_data = []  # 清空数据

        # 处理最后一批数据
        results = await asyncio.gather(*tasks)
        completed_data = list(chain.from_iterable(results))
        if completed_data:
            insert_klines(cursor, table_name, completed_data)
            conn.commit()
            logging.info(f"Inserted {len(completed_data)} klines to database.")

    # 提交事务并关闭连接
    cursor.close()
    conn.close()

# 输入年份，返回该年份所有日期的开始和结束时间戳对的列表


def get_year_timestamps(start_year, end_year):
    start = datetime(start_year, 1, 1)
    end = datetime(end_year + 1, 1, 1)
    timestamps = []
    while start < end:
        timestamps.append((int(start.timestamp() * 1000),
                          int((start + timedelta(days=1)).timestamp() * 1000 - 1)))
        start += timedelta(days=1)
    return timestamps


if __name__ == "__main__":
    symbol = "BTCUSDT"
    start_year = 2020  # 修改为所需的开始年份
    end_year = 2024    # 修改为所需的结束年份
    asyncio.run(main(symbol, start_year, end_year))
