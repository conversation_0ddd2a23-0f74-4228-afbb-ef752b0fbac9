import pandas as pd
import numpy as np


class TechFactors:

    @staticmethod
    def c_Tech001(df, window=14):
        '''计算atr因子'''
        tr1 = df['high'] - df['low']
        tr2 = abs(df['high'] - df['close'].shift(1))
        tr3 = abs(df['close'].shift(1) - df['low'])
        tr = pd.DataFrame({'tr1': tr1, 'tr2': tr2, 'tr3': tr3}).max(axis=1)
        atr = tr.ewm(alpha=1 / window, adjust=False).mean()
        return atr

    @staticmethod
    def c_Tech002(df, window=14):
        '''计算rsi因子'''
        close_diff = df['close'].diff()
        u = np.where(close_diff > 0, close_diff, 0)
        d = np.where(close_diff < 0, -close_diff, 0)
        smma_u = pd.Series(u).ewm(alpha=1 / window, adjust=False).mean()
        smma_d = pd.Series(d).ewm(alpha=1 / window, adjust=False).mean()
        rsi = pd.Series(np.where(smma_d == 0, 100,
                                 np.where(smma_u == 0, 0,
                                          100 * (smma_u / (smma_u + smma_d)))), index=df.index)
        return rsi

    @staticmethod
    def c_Tech003(df, short_period=10, long_period=30):
        c = df.close
        # 计算每日收益率的对数
        log_returns = np.log(c / c.shift(1))
        # 计算短期HV
        hv_short = log_returns.rolling(
            window=short_period).std() * np.sqrt(252)  # 年化波动率
        # 计算长期HV
        hv_long = log_returns.rolling(
            window=long_period).std() * np.sqrt(252)  # 年化波动率
        # 计算HV比率
        hv_ratio = hv_short / hv_long

        return hv_ratio

    def c_Tech004(df, short_window=5, long_window=34):
        close = df['close']
        high = df['high']
        low = df['low']

        mid_price = (df['high']+df['low'])/2
        short_sma = mid_price.rolling(window=short_window).mean()
        long_sma = mid_price.rolling(window=long_window).mean()
        ao = short_sma - long_sma

        return ao

    def c_Tech005(df, period=14):
        c = df.close  # 获取收盘价

        # 计算威廉指标（Williams %R）
        highest_high = df.high.rolling(window=period).max()  # 过去14天的最高价
        lowest_low = df.low.rolling(window=period).min()     # 过去14天的最低价
        williams_r = -100 * (highest_high - c) / \
            (highest_high - lowest_low)  # Williams %R 计算

        return williams_r

    @staticmethod
    def c_Tech006(df, fast=12, slow=26, signal=9):
        '''计算MACD因子'''
        close = df['close']
        ema_fast = close.ewm(span=fast, adjust=False).mean()
        ema_slow = close.ewm(span=slow, adjust=False).mean()
        macd_line = ema_fast - ema_slow
        signal_line = macd_line.ewm(span=signal, adjust=False).mean()
        macd_hist = macd_line - signal_line
        return pd.Series(macd_hist, index=df.index)
