from abc import ABC, abstractmethod
from typing import List, Dict, Any
import pandas as pd


class Alpha(ABC):
    """抽象策略基类（自动校验+子类仅实现核心逻辑）"""

    def __init__(self):
        super().__init__()

    @property
    @abstractmethod
    def required_fields(self) -> List[str]:
        """必须字段列表（子类实现）"""
        pass

    @property
    @abstractmethod
    def kline_period(self) -> int:
        """需要的历史K线数量(子类实现)"""
        pass

    def execute(self, data: pd.DataFrame, position_dict: dict) -> list:
        """
        对外统一入口（自动校验输入）
        子类无需覆盖此方法！
        """
        # 校验属性合法性
        self._validate_properties()
        # 校验输入数据合法性
        self._validate_input(data, position_dict)
        # 调用子类实现的逻辑
        return self.on_data(data, position_dict)

    @abstractmethod
    def on_data(self, data: pd.DataFrame, position_dict: dict) -> list:
        """子类需实现的策略逻辑（无需处理校验）"""
        pass

    @abstractmethod
    def get_params(self) -> Dict[str, Any]:
        """获取策略参数（子类实现）"""
        pass

    def _validate_properties(self) -> None:
        """校验子类属性合法性"""
        # 检查 required_fields
        if (
            not isinstance(self.required_fields, list) or
            len(self.required_fields) == 0 or
            not all(isinstance(f, str) for f in self.required_fields)
        ):
            raise AttributeError(
                f"required_fields 必须是非空字符串列表，当前值: {self.required_fields}"
            )

        # 检查 kline_period
        if not isinstance(self.kline_period, int) or self.kline_period <= 0:
            raise AttributeError(
                f"kline_period 必须是正整数，当前值: {self.kline_period}"
            )

    def _validate_input(self, data: pd.DataFrame, position_dict: dict) -> None:
        """校验输入数据合法性"""
        if not isinstance(data, pd.DataFrame):
            raise TypeError(f"需要DataFrame类型，实际收到: {type(data)}")
        if len(data) != self.kline_period:
            raise ValueError(
                f"K线数据长度不符！需要 {self.kline_period} 根，实际 {len(data)} 根"
            )
        missing_fields = [
            f for f in self.required_fields if f not in data.columns]
        if missing_fields:
            raise ValueError(f"缺少必要字段: {missing_fields}")
        if not (0 <= position_dict['long_num'] <= 1):
            raise ValueError(f"非法仓位值: {position_dict['long_num']} (必须在0~1之间)")


class MovingAverageStrategy(Alpha):
    """均线策略"""

    def __init__(self, lookback: int = 30, ma_window: int = 20):
        super().__init__()
        self._required_fields = ['close']  # 定义必要字段
        self._kline_period = lookback      # 定义K线长度
        self.ma_window = ma_window         # 策略参数
        """
            open_position_records: [
                {
                   date<开仓时间>,
                   volume<开仓数量 正：多仓，负：空仓>,
                   side<仓位方向，SHORT,LONG>,
                   avg_price<开仓均价>
                }
            ]
        """
        self.open_position_records = []    # 开仓记录

    @property
    def required_fields(self) -> List[str]:
        return self._required_fields.copy()

    @property
    def kline_period(self) -> int:
        return self._kline_period

    """
    params:
        data: 行情data
        position_dict: {
            'long_num': 0, // 多仓数量
            'long_avg_cost': 0.0, // 多仓均价
            'short_num': 0, // 空仓数量
            'short_avg_cost': 0.0, // 空仓均价
            'available_cash': 50000 // 可用余额
        }
        
    return:
        list<dict>:
            [{
                # 交易信号（买卖信号，数值同配置的买卖信号数值）
                'signal': signal_t,
                # 交易方向 SHORT，LONG
                'side': record['side'],
                # 交易数量
                'trade_volume': record['volume']
            }]
    """

    def on_data(self, data: pd.DataFrame, position_dict: dict) -> list:
        """策略逻辑"""
        now_data = data.iloc[-1]
        # 检测开仓记录、生成平仓单
        # 当前时间大于开仓时间 96根k以上(15m级)
        def condition(
            x): return now_data['close_time'] - x['date'] >= 96 * 15 * 60 * 1000
        # 已到平仓时间的开仓数据
        need_close_records = [
            x for x in self.open_position_records if condition(x)]
        # 从原列表中删除（已处理的开仓记录数据） 这里是需要直接修改原数据
        self.open_position_records[:] = [
            x for x in self.open_position_records if not condition(x)]  # 原地修改
        trade_arr = []
        # 可以优化成平多 和 平空 两个交易单，就不用每个append
        for record in need_close_records:
            # todo: 写入平仓交易单trade_arr.append()
            signal_t = -1
            if record['side'] == 'LONG':
                signal_t = -1
            elif record['side'] == 'SHORT':
                signal_t = 1
            else:
                continue
            # 写入平仓单
            trade_arr.append({
                # 交易信号（数值同配置的买卖信号数值）
                'signal': signal_t,
                # 交易方向 SHORT LONG
                'side': record['side'],
                # 交易数量
                'trade_volume': record['volume'],
                # 交易价格
                'executed_price': now_data['close'],
            })
        # 策略逻辑，生成响应的开仓单
        strategy_trade_volume = 1
        # 策略的开仓单
        strategy_open = {
            # 交易信号（1买，-1卖）
            'signal': 1,
            # 交易方向 SHORT LONG
            'side': 'LONG',
            # 交易数量
            'trade_volume': strategy_trade_volume,
        }

        # 开仓记录
        open_record = {
            'date': now_data['close_time'],
            'volume': strategy_trade_volume,
            'side': strategy_open['side'],
            'avg_price': now_data['close'],
        }
        self.open_position_records.append(open_record)
        trade_arr.append(strategy_open)
        return trade_arr

    def get_params(self) -> Dict[str, Any]:
        return {
            'kline_period': self.kline_period,
            'required_fields': self.required_fields,
            'ma_window': self.ma_window
        }


if __name__ == '__main__':
    # 初始化策略
    strategy = MovingAverageStrategy(lookback=30, ma_window=20)

    # 准备测试数据
    test_data = pd.DataFrame(
        {'close': [i * 1.1 for i in range(30)], 'close_time': [i * 1.1 for i in range(30)]})

    position_dict = {
        'long_num': 0,
        'long_avg_cost': 0.0,
        'short_num': 0,
        'short_avg_cost': 0.0,
        'available_cash': 50000
    }

    # 执行策略（自动校验）
    try:
        trad_arr = strategy.execute(test_data, position_dict)
        print(f"交易单：{trad_arr}")
    except Exception as e:
        print(f"策略执行失败: {str(e)}")

    # 查看参数
    print("策略参数:", strategy.get_params())
